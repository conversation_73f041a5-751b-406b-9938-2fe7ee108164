plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("com.google.gms.google-services")
    id("com.google.firebase.crashlytics")
    id("dagger.hilt.android.plugin")
    id("org.jetbrains.kotlin.kapt")
    id("config.values")
    id("keyless.koin.android")
    alias(keyless.plugins.compose.compiler)
}

extra.apply {
    set("androidAppId", "com.keyless_dubai")
}
val depMgmt by extra {
    mapOf(
        "androidSupportAppCompatV7" to "com.android.support:appcompat-v7:28.0.0",
        "androidSupportDesign" to "com.android.support:design:28.0.0",
        "androidSupportConstraintLayout" to "com.android.support.constraint:constraint-layout:1.1" +
                ".3",
        /* Bouncy Castle (Crypto, used by JavaCSL library) */
        "bouncyCastleBcprov" to "org.bouncycastle:bcprov-jdk15on:1.59",
        "gson" to "com.google.code.gson:gson:2.8.5",
        "archAnnotation" to "android.arch.persistence.room:compiler:1.1.1",
        "arch" to "android.arch.persistence.room:runtime:1.1.1",
        "msgpack" to "org.msgpack:msgpack-core:0.8.16",
        "commonsCodec" to "commons-codec:commons-codec:1.10",
        /* SLF4J (logging, used e.g. by io-simplybt libraries) */
        "slf4jApi" to "org.slf4j:slf4j-api:1.7.26",
        "slf4jNop" to "org.slf4j:slf4j-nop:1.7.26",
        // "slf4jAndroid" to "org.slf4j:slf4j-android:1.7.26",,
        /* see:  https://github.com/nomis/slf4j-android */
        "slf4jAndroid" to "uk.uuid.slf4j:slf4j-android:1.7.25-1",
        // ## 3rd party test-only dependencies ##,
        "junit" to "junit:junit:4.12",
        "mockitoCore" to "org.mockito:mockito-core:1.10.19",
        "androidEspressoCore" to "com.android.support.test.espresso:espresso-core:3.5.0",
    )
}

val environment = getLocalProperty("environment")
    ?: throw IllegalStateException("environment is not set")

android {
    val androidAppId: String by extra

    namespace = "com.app.keyless"
    compileSdk = keyless.versions.androidSdkCompile.get().toInt()

    bundle {
        language {
            // TODO Test on store
            enableSplit = false
        }
    }
//    splits {
//        abi {
//            enable true
//            reset()
//            include 'armeabi-v7a'
//            include 'armeabi-v8a'
//            universalApk false
//        }
//    }

    defaultConfig {
        multiDexEnabled = true
        applicationId = androidAppId
        minSdk = 29
        targetSdk = 34
        versionCode = 109
        versionName = "2.5.13"
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        resourceConfigurations.addAll(listOf("en", "ar"))
//        externalNativeBuild {
//            cmake {
//                abiFilters "arm64-v8a"
//            }
//        }
    }

    signingConfigs {
        create("signing") {
            val keyStoreFile = getLocalProperty("release.signing.key.store")?.toString()
                ?: throw IllegalStateException("release.signing.key.store is not set")
            val keyStoreStorePassword = getLocalProperty("release.signing.key.store.password")?.toString()
                ?: throw IllegalStateException("release.signing.key.store.password is not set")
            val keyStoreKeyAlias = getLocalProperty("release.signing.key.alias")?.toString()
                ?: throw IllegalStateException("release.signing.key.alias is not set")
            val keyStoreKeyPassword = getLocalProperty("release.signing.key.password")?.toString()
                ?: throw IllegalStateException("release.signing.key.password is not set")

            storeFile = rootProject.file(keyStoreFile)
            storePassword = keyStoreStorePassword
            keyAlias = keyStoreKeyAlias
            keyPassword = keyStoreKeyPassword
        }
    }

    buildTypes {
        getByName("release") {
            isShrinkResources = true
            isMinifyEnabled = true
            signingConfig = signingConfigs.getByName("signing")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            )
            the<com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsExtension>()
                .mappingFileUploadEnabled = environment.toString().lowercase() != "development"
        }

         getByName("debug") {
             isShrinkResources = false
             isMinifyEnabled = false
             proguardFiles(
                 getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
             )
             isJniDebuggable = true
         }

    }

    compileOptions {
        sourceCompatibility = JavaVersion.toVersion(keyless.versions.javaCompatibility.get())
        targetCompatibility = JavaVersion.toVersion(keyless.versions.javaCompatibility.get())
    }

    kotlinOptions {
        jvmTarget = keyless.versions.javaCompatibility.get()
    }

    sourceSets {
        getByName("main") {
            jniLibs.srcDir("libs")
        }
    }
    buildFeatures {
        viewBinding = true
        dataBinding = true
        compose = true
    }
    lintOptions {
        isCheckReleaseBuilds = false
    }

    kapt {
        correctErrorTypes = true
    }

    androidResources {
        noCompress += "Regula/faceSdkResource.dat"
    }

    aaptOptions {
        noCompress += "Regula/faceSdkResource.dat"
    }
}

dependencies {
    // implementation(project(":ttlockSdk")

    implementation(project(":core-common"))
    implementation(project(":core-lock-ttlock"))
    implementation(project(":core-caching-key-value"))
    implementation(project(":core-monitoring-common"))
    implementation(project(":core-monitoring-firebase"))
    implementation(project(":core-regula-documents"))
    implementation(project(":core-regula-face"))
    implementation(project(":data-common"))
    implementation(project(":data-network-android"))
    implementation(project(":data-utils-android"))
    implementation(project(":domain-home"))
    implementation(project(":feature-pm-addlock"))
    implementation(project(":feature-injection"))
    implementation(project(":feature-qrcode-scan"))
    implementation(project(":feature-common"))
    implementation(project(":feature-dashboard"))
    implementation(project(":feature-dfu"))
    implementation(project(":feature-home-admin"))
    implementation(project(":feature-home-locks"))
    implementation(project(":feature-home-installer"))
    implementation(project(":feature-home-properties"))
    implementation(project(":feature-master-key"))
    implementation(project(":feature-notifications"))
    implementation(project(":feature-onboarding"))
    implementation(project(":feature-properties"))
    implementation(project(":feature-regula"))
    implementation(project(":feature-regula-refactored"))
    implementation(project(":feature-routines"))
    implementation(project(":feature-security"))
    implementation(project(":feature-settings"))
    implementation(project(":feature-settings-checkin"))
    implementation(project(":feature-settings-company-profile"))
    implementation(project(":feature-settings-company-staff"))
    implementation(project(":feature-settings-profile"))
    implementation(project(":feature-settings-support"))
    implementation(project(":presentation-authentication"))
    implementation(project(":presentation-home"))

    implementation("androidx.multidex:multidex:2.0.1")
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("com.google.android.material:material:1.9.0")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("com.google.firebase:firebase-messaging-ktx:23.2.1")
    implementation("com.google.android.gms:play-services-maps:18.1.0")
    implementation("com.google.android.gms:play-services-location:21.0.1")
    implementation("com.google.android.libraries.places:places:3.2.0")
    implementation("androidx.lifecycle:lifecycle-service:2.6.2")
    implementation("androidx.biometric:biometric-ktx:1.2.0-alpha05")
    implementation("com.google.android.play:app-update-ktx:+")
    implementation("androidx.navigation:navigation-fragment-ktx:2.5.3")
    implementation("androidx.navigation:navigation-ui-ktx:2.5.3")
    implementation("androidx.compose.material3:material3:1.1.2")
    implementation("androidx.compose.ui:ui-tooling-preview-android:1.5.2")
    implementation(keyless.androidx.compose.activity)
    implementation(keyless.androidx.compose.material.icons.extended)
    implementation(keyless.nordic.navigation)
    implementation(keyless.nordic.theme)
    implementation(keyless.nordic.ui.logger)

    implementation("androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2")
    implementation("androidx.lifecycle:lifecycle-extensions:2.2.0")
    kapt("androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2")
    implementation("androidx.activity:activity-ktx:1.7.2")
    implementation("androidx.fragment:fragment-ktx:1.6.1")
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.0")
    implementation("com.hbb20:ccp:2.5.1")
    implementation("de.hdodenhof:circleimageview:3.1.0")
    implementation("io.github.chaosleung:pinview:1.4.4")
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.1")
    implementation("com.squareup.okhttp3:logging-interceptor:5.0.0-alpha.2")
    //Coroutines and LifeCycle Libraries
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0-alpha02")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.6.2")
    //LifeCycle
    implementation("androidx.lifecycle:lifecycle-common:2.6.2")
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2")
    implementation(platform("com.google.firebase:firebase-bom:28.4.2"))
    implementation("com.google.firebase:firebase-firestore-ktx:24.8.1")
    implementation(keyless.glide)
    kapt(keyless.glide.compiler)
    implementation("org.greenrobot:eventbus:3.3.1")

    implementation(depMgmt.get("bouncyCastleBcprov")!!)
    implementation(depMgmt.get("gson")!!)
    implementation(depMgmt.get("slf4jApi")!!)
    implementation(depMgmt.get("slf4jNop")!!)
    implementation(depMgmt.get("msgpack")!!)
    implementation(depMgmt.get("commonsCodec")!!)
    implementation(project(":core-lock-iseo"))
    implementation(files("libs/rayo.key.sdk.jar"))

    implementation(project(":core-lock-mst"))
    implementation("com.mikhaellopez:circularimageview:4.3.1")
    implementation("me.dm7.barcodescanner:zxing:1.9.8")
    implementation("com.karumi:dexter:6.2.3")
    implementation("com.skyfishjy.ripplebackground:library:1.0.1")
    implementation("com.github.codersrouteandroid:flexible-switch:1.0")
    implementation("com.alibaba:fastjson:1.1.60.android")
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))
    implementation ("com.github.khoyron:Actionsheet-android:4")
    implementation ("com.github.rajputkapil:textviewdotsanimation:v1.0")
    implementation ("pub.devrel:easypermissions:3.0.0")

    implementation ("com.google.firebase:firebase-crashlytics-ktx")
    implementation ("androidx.swiperefreshlayout:swiperefreshlayout:1.1.0")
    implementation ("androidx.work:work-runtime:2.8.1")
    //room
    implementation ("androidx.room:room-runtime:2.5.2")
    kapt ("androidx.room:room-compiler:2.5.2")
    androidTestImplementation ("androidx.room:room-testing:2.5.2")
    implementation ("androidx.room:room-ktx:2.5.2")
    implementation ("com.github.mukeshsolanki:android-otpview-pinview:3.2.0")
    implementation ("com.wdullaer:materialdatetimepicker:4.2.3")
    implementation ("com.github.vedraj360:DesignerToast:0.1.3")
//    implementation(project(":lib:analytics"))
//    implementation(project(":lib:storage")) // Deep link support
//    implementation(project(":profile:navigation"))
    implementation ("com.google.dagger:hilt-android:2.53.1")
    kapt ("com.google.dagger:hilt-android-compiler:2.53.1")

    implementation ("no.nordicsemi.android:dfu:2.4.2")
    implementation ("com.github.kittinunf.fuel:fuel:2.3.1")

//    implementation(name: 'TTLockOnPremise', ext: 'aar')
    implementation (fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))
    implementation (fileTree(mapOf("dir" to "libs", "include" to listOf("*.aar"))))

    implementation(keyless.ktor.client.android)
}

configValues {
    val baseUrl = getLocalProperty("base.url") as String? ?: throw GradleException(
        "Missing base.url property in local.properties"
    )
    val baseImageUrl = getLocalProperty("base.image.url") as String? ?: throw GradleException(
        "Missing base.image.url property in local.properties"
    )
    val forgotPasswordUrl = getLocalProperty("forgot.password.url") as String? ?: throw GradleException(
        "Missing forgot.password.url property in local.properties"
    )

    setValues(
        "baseUrl" to baseUrl,
        "baseImageUrl" to baseImageUrl,
        "forgotPasswordUrl" to forgotPasswordUrl
    )
}