package domain.settings.checkin.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.user.guest.repositories.GuestRepository
import domain.settings.checkin.repositories.ScreenStateRepository
import domain.settings.checkin.usecases.screen.InitScreenUseCase

internal class SettingsCheckInUseCases(
    screen: ScreenStateRepository,
    repository: GuestRepository,
    logger: Logger,
    status: StatusRepository
) {
    val init = InitScreenUseCase(
        screen = screen,
        repository = repository,
        logger = logger,
        status = status
    )
}