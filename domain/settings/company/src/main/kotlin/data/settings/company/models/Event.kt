package data.settings.company.models

sealed interface Event

sealed interface ScreenEvent : Event {
    object Init : ScreenEvent
}

sealed interface CompanyEvent : Event {

    class UpdateCompany(
        val companyName: String,
        val address: String,
        val country: String,
        val city: String,
        val trnNumber: String,
        val zipCode: String,
        val checkIn: Boolean,
        val businessType: String,
        val businessLia: String,
        val tradeLicenseNumber: String,
        val timezoneOffset: String,
        val timezoneName: String
    ) : CompanyEvent
}