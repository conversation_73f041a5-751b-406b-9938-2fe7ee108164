package domain.settings.support.repositories

import core.common.status.Status
import core.common.status.StatusRepository
import domain.settings.support.models.ScreenState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update

class ScreenStateRepository(
    private val status: StatusRepository
) {

    private val state = MutableStateFlow(emptyState())
    private var currentState = state.value
    val stream = combination(
        screenState = state,
        status = status.stream
    ) { screen, status ->
        screen.copy(
            status = status.filter { it !is Status.Loading }
        )
    }.onEach { currentState = it }

    fun get() = currentState

    fun update(block: ScreenStateRepository.(ScreenState) -> ScreenState) {
        state.update { block(it) }
    }
}

val ScreenState.nearby get(): ScreenState.State.Nearby? {

    if (state !is ScreenState.State.Nearby) return null

    return state
}