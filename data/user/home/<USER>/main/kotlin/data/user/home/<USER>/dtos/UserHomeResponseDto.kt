package data.user.home.models.dtos

import data.lock.common.lock.models.lock.UserHomeResponse
import kotlinx.serialization.Serializable

@Serializable
internal data class UserHomeResponseDto(
    var locks: ArrayList<LockDetailsDto> = ArrayList(),
    var properties: ArrayList<PropertyDto> = ArrayList(),
    var success: Boolean = false,
    var message: String = "",
    var assignment_id: String = "",
    var logout: Boolean = false,
    var is_paid: Boolean = false,
    var totalUnreadNotification: Int = 0
) {
    fun toModel(): UserHomeResponse {
        return UserHomeResponse(
            locks = locks.map { it.toModel() },
            properties = properties.map { it.toModel() },
            success = success,
            message = message,
            assignmentId = assignment_id,
            logout = logout,
            isPaid = is_paid,
            totalUnreadNotification = totalUnreadNotification
        )
    }
}