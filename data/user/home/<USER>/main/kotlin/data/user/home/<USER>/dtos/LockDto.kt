package data.user.home.models.dtos

import kotlinx.serialization.Serializable

@Serializable
internal data class LockDto(
    val __v: Int = 0,
    val _id: String = "",
    val access_key: String = "",
    val alloted: Boolean = false,
    val battery_level: Int = -1,
    val createdAt: String = "",
    val image: String = "",
    val lock_uid: String = "",
    val name: String = "",
    val desc: String = "",
    val provider: String = "",
    val status: Int = -1,
    val unique_key: String = "",
    val unit_id: String = "",
    val messer_token: String = "",
    val time_zone: String = "",
    val internal_id: String = "",
    val encrypted_key: String = "",
    val privacy_mode: Boolean = false,
    val primary: Boolean = false,
    val privacy_permission: Boolean = false,
    val privacy_owner: Boolean = false,
    val icon: ArrayList<IconDto> = ArrayList(),
    val firmware_updated: Boolean = false,
    val firmwareVersion: String? = "",
    val firmwareAvailable: String? = "",
    val tedeeLockId: String? = ""
)