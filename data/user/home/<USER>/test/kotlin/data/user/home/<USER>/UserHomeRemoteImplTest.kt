package data.user.home.remote

import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.http.client.test.MockHttpClient
import core.monitoring.common.repository.Logger
import data.test.JsonResponse
import data.test.authToken
import data.test.mockRequest
import data.test.testHostname
import data.test.testHttpClient
import data.test.testLogger
import data.user.home.models.dtos.UserHomeResponseDto
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals

class UserHomeRemoteImplTest {

    private lateinit var sample: UserHomeRemoteImpl
    private lateinit var client: MockHttpClient
    private lateinit var logger: Logger

    @BeforeTest
    fun setup() {
        client = testHttpClient()
        logger = testLogger()
        sample = UserHomeRemoteImpl(
            client = client,
            logger = logger,
            hostname = testHostname
        )
    }

    @Test
    fun getLocksList() = runTest {
        val response = JsonResponse("user-home-response.json")
        val serialized = response.value<UserHomeResponseDto>()
        val url = "$testHostname/user/home"
        val uid = "1234567890"
        val isAdmin = false
        val body = buildJsonObject {
            put("uid", uid)
            put("is_admin", isAdmin)
        }
        val headers = listOf(
            HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = authToken),
            HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
        )
        val mock = mockRequest(
            url = url,
            requestBody = body,
            responseBody = response.raw,
            method = HttpRequest.Method.POST,
            requestHeaders = headers
        )

        client.setup(mock)

        val result = sample.getLocksList(uid, isAdmin, authToken)

        assertNotEquals(result.request, mock.request.copy(url = ""))
        assertEquals(result.request, mock.request)

        assertNotEquals(result.value(UserHomeResponseDto.serializer()), serialized.copy(is_paid = !serialized.is_paid))
        assertEquals(result.value(UserHomeResponseDto.serializer()), serialized)
    }
}