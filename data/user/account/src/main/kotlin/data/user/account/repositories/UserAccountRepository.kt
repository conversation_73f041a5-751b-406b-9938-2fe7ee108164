package data.user.account.repositories

import data.user.account.models.SendOtpEmailResponse
import data.user.account.models.UserProfileResponse
import data.user.account.models.VerifyEmailResponse

interface UserAccountRepository {

    suspend fun sendOtpMail(email: String): SendOtpEmailResponse

    suspend fun verifyEmail(
        email: String,
        otp: String,
        method: String = "email",
        deviceType: String,
        userId: String
    ): VerifyEmailResponse

    suspend fun changePassword(
        oldPassword: String,
        newPassword: String,
        confirmPassword: String,
        userId: String,
        isAdmin: Boolean
    ): String

    suspend fun updateProfile(
        firstName: String,
        lastName: String
    ): String

    suspend fun checkIn(
        name: String,
        bookingNumber: String,
        documentNumber: String,
        status: String,
        documentType: String,
        similarity: String,
        expiryDate: String,
        identity1: ByteArray,
        identity2: ByteArray?,
        documentImage: ByteArray,
        capturedImage: ByteArray,
        extra: String
    ): String

    suspend fun getUserProfile(): UserProfileResponse

    suspend fun deleteAccount(): Unit
}