plugins {
    id("keyless.kotlin")
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":core-http-client-common"))
    implementation(project(":core-monitoring-common"))
    implementation(project(":core-lock-common"))
    implementation(project(":core-lock-logs"))
    implementation(project(":core-locks-manager"))

    implementation(project(":data-common"))
    implementation(project(":data-database"))
    api(project(":data-keyless"))
}