package data.lock.common.lock.repositories

import core.http.client.HttpResponse

interface LocksRemote {

    suspend fun upgradeMaintenance(lockId: String, authorization: String): HttpResponse

    suspend fun downgradeMaintenance(lockId: String, authorization: String): HttpResponse

    suspend fun getDetails(lockName: String, uid: String, authorization: String): HttpResponse

    suspend fun deleteLock(lockId: String, authorization: String): HttpResponse

    suspend fun fetchPlatformDetails(authorization: String): HttpResponse
}