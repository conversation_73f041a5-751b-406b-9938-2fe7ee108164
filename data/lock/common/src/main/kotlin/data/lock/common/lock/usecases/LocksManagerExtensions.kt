package data.lock.common.lock.usecases

import core.lock.common.models.LockBrand
import core.lock.iseo.models.IseoLock
import core.lock.rayonics.models.RayonicsLock
import core.lock.ttlock.models.TTLock
import core.locks.logs.models.LockLogActionType
import core.locks.manager.LocksManager
import core.monitoring.common.repository.async
import data.common.preferences.Constants
import data.common.preferences.Preferences
import data.lock.common.lock.models.lock.Lock
import data.lock.common.lock.repositories.LocksRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Clock

suspend fun LocksManager.resetTime(
    lock: Lock,
    locks: LocksRepository
) = logger.async() {
    resetTime(
        provider = lock.provider,
        uniqueKey = lock.uniqueKey,
        accessKey = lock.accessKey,
        id = lock.id,
        internalId = lock.internalId,
        locks = locks
    )
}

suspend fun LocksManager.resetTime(
    provider: String,
    uniqueKey: String,
    accessKey: String,
    id: String,
    internalId: String,
    locks: LocksRepository
) = logger.async() {
    when (provider) {
        Constants.keyless -> {
            val rayonics = getOrScanForRayonicsLock(uniqueKey)
            rayonics.connect(accessKey)
            rayonics.resetLockTime()
            kotlin.runCatching { rayonics.disconnect() }
        }

        Constants.iseo -> withContext(Dispatchers.IO) {
            val iseo = getOrScanForIseoLock(uniqueKey)

            locks.upgradeMaintenance(id, Preferences.authenticationToken.get())
            delay(2000)

            iseo.refreshCredentials()

            iseo.enterInMaintenanceMode()
            logIseoInfo(iseo, message = "Get info before reset time", internalId = internalId)

            iseo.resetLockTime()

            logIseoInfo(iseo, message = "Get info after reset time", internalId = internalId)
            iseo.exitFromMaintenanceMode()

            locks.downgradeMaintenance(id, Preferences.authenticationToken.get())

            // Important delay between downgrade maintenance and refreshing credentials
            delay(2000)
            iseo.refreshCredentials()
        }

        Constants.oji, Constants.linko -> {
            val ttlock = getOrScanTTLock(uniqueKey)
            ttlock.resetLockTime(accessKey)
        }

        Constants.lockWise -> {
            val airbnk = getAirBnkLock(accessKey)
            airbnk.setLockTime(Clock.System.now().toEpochMilliseconds())
            airbnk.disconnect()
        }

        else -> {}
    }
}

suspend fun LocksManager.unlock(
    lock: Lock
) = logger.async() {
    unlock(
        provider = lock.provider,
        uniqueKey = lock.uniqueKey,
        accessKey = lock.unlockAccessKey(),
        id = lock.id,
        lockUid = lock.lockUid
    )
}

suspend fun LocksManager.unlock(
    provider: String,
    uniqueKey: String,
    accessKey: String,
    lockUid: String,
    id: String
) = logger.async() {
    val timeout = 20_000L
    when (provider) {
        Constants.keyless -> {
            val rayonics = getOrScanForRayonicsLock(uniqueKey)
            rayonics.connect(accessKey)
            rayonics.unlock()
            kotlin.runCatching { rayonics.disconnect() }
        }

        Constants.iseo -> withContext(Dispatchers.IO) {
            val iseo = getOrScanForIseoLock(uniqueKey)
            iseo.unlock()
        }

        Constants.oji, Constants.linko -> {
            val ttlock = getOrScanTTLock(uniqueKey)
            ttlock.unlock(accessKey)
        }

        Constants.lockWise -> {
            val airbnk = getAirBnkLock(accessKey)
            airbnk.unlock()
            airbnk.disconnect()
        }

        Constants.tedee -> {
            val location = getLocation()
            val first = runCatching {
                getTedeeRepository().unlock(
                    lockId = id,
                    authentication = Preferences.authenticationToken.get(),
                    role = Preferences.userRole.get(),
                    longitude = location.long,
                    latitude = location.lat
                )
            }

            if (first.exceptionOrNull() != null) {
                runCatching {
                    getTedeeRepository().unlockAssignment(
                        lockId = id,
                        authentication = Preferences.authenticationToken.get()
                    )
                }.onFailure { it.printStackTrace() }.getOrNull() ?: throw first.exceptionOrNull()!!
            }

            first
        }

        Constants.messerSchimtt -> {
            val lock = getMSTLock(accessKey = accessKey, uniqueKey = uniqueKey, lockUid = lockUid)
            lock.unlock()
        }

        else -> {}
    }
}

suspend fun LocksManager.lock(lock: Lock) = logger.async() {
    lock(provider = lock.provider, accessKey = lock.accessKey, id = lock.id)
}

suspend fun LocksManager.lock(provider: String, accessKey: String, id: String) = logger.async() {
    when (provider) {
        Constants.tedee -> {
            val location = getLocation()
            getTedeeRepository().lock(
                lockId = id,
                authentication = Preferences.authenticationToken.get(),
                role = Preferences.userRole.get(),
                longitude = location.long,
                latitude = location.lat
            )
        }

        Constants.lockWise -> {
            val airbnk = getAirBnkLock(accessKey)
            airbnk.lock()
            airbnk.disconnect()
        }

        else -> {}
    }
}

suspend fun LocksManager.resetIseoTime(id: String, uniqueKey: String, locks: LocksRepository) = logger.async() {
    withContext(Dispatchers.IO) {
        locks.upgradeMaintenance(id, Preferences.authenticationToken.get())
        delay(2000)

        val iseo = getIseoLock(uniqueKey) ?: throw Exception("Iseo lock not found")

        iseo.refreshCredentials()

        iseo.enterInMaintenanceMode()

        iseo.resetLockTime()

        iseo.exitFromMaintenanceMode()

        locks.downgradeMaintenance(id, Preferences.authenticationToken.get())

        // Important delay between downgrade maintenance and refreshing credentials
        delay(2000)
        iseo.refreshCredentials()
    }
}

private suspend fun logIseoInfo(
    lock: IseoLock,
    message: String,
    internalId: String
) {
    try {
        Preferences.lockLogger.log(
            actionType = LockLogActionType.Maintenance.toString(),
            lockInternalId = internalId,
            message = message,
            lockInfoData = lock.getLockInfoString(),
            provider = Constants.iseo,
            user = Preferences.userFullName.get()
        )
    } catch (ex: Exception) {
        Preferences.lockLogger.log(
            actionType = LockLogActionType.Maintenance.toString(),
            lockInternalId = internalId,
            message = "Failed to: $message",
            lockInfoData = ex.message ?: "Error Happened",
            provider = Constants.iseo,
            user = Preferences.userFullName.get()
        )
    }
}

private fun Lock.log(
    actionType: LockLogActionType,
    message: String,
    data: String
) {
    CoroutineScope(Dispatchers.IO).launch {
        Preferences.lockLogger.log(
            actionType = actionType.toString(),
            lockInternalId = internalId,
            message = message,
            lockInfoData = data,
            provider = provider,
            user = Preferences.userFullName.get()
        )
    }
}

private suspend fun LocksManager.getOrScanForIseoLock(
    uniqueKey: String,
    timeout: Long = 20_000
): IseoLock {
    return getIseoLock(uniqueKey) ?: kotlinx.coroutines.withTimeoutOrNull(timeout) {
        scan(lockBrands = listOf(LockBrand.ISEO)).first { it.name == uniqueKey }
        getIseoLock(uniqueKey)
    } ?: throw Exception("Lock is not nearby.")
}

private suspend fun LocksManager.getOrScanForRayonicsLock(
    uniqueKey: String,
    timeout: Long = 20_000
): RayonicsLock {
    return getRayonicsLock(uniqueKey) ?: kotlinx.coroutines.withTimeoutOrNull(20_000) {
        scan(lockBrands = listOf(LockBrand.Rayonics)).first { it.name == uniqueKey }
        getRayonicsLock(uniqueKey)
    } ?: throw Exception("Lock is not nearby.")
}

private suspend fun LocksManager.getOrScanTTLock(
    uniqueKey: String,
    timeout: Long = 20_000
): TTLock {
    return getTTLock(uniqueKey) ?: kotlinx.coroutines.withTimeoutOrNull(20_000) {
        scan(lockBrands = listOf(LockBrand.TTLock)).first { it.name == uniqueKey }
        getTTLock(uniqueKey)
    } ?: throw Exception("Lock is not nearby.")
}