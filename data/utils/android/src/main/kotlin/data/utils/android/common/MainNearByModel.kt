package data.utils.android.common

import androidx.annotation.Keep
import data.network.android.LocksListResponse

@Keep
class MainNearByModel(
    var lockType: Int = 0,
    var igo: LockInfo?,
    var keyless: BleLockScanData?,
    var apiLok: LocksListResponse.LockModel?,
    var isAvailable: Boolean,
    var propertyDetails: LocksListResponse.PropertyDetailsModel?,
    val isUpdateAvailable: Boolean,
    val availableVersion: String,
    val installedVersion: String
)