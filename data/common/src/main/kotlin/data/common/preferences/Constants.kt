package data.common.preferences

object Constants {

    val appCoroutineScopeName = "AppCoroutineContext"

    val PROFILE_STATUS_REQUIRE_PHONE_NUMBER = 4
    val PROFILE_STATUS_VERIFY_EMAIL = 5

    val iseo: String = "ISEO"
    val messerSchimtt: String = "Messerschmitt"
    val keyless: String = "Keyless"
    val oji: String = "Oji"
    val linko: String = "Linko"
    val lockWise = "LockWise"
    val tedee = "Tedee"
}

@Suppress("EnumEntryName", "ktlint:enum-entry-name-case")
enum class UserType {
    Admin {
        override fun toString(): String = "Admin"
    },
    Guest {
        override fun toString(): String = "Guest"
    },
    owner {
        override fun toString(): String = "owner"
    }
}