package data.company.repositories

import core.http.client.HttpResponse

interface CompanyRemoteRepository {

    suspend fun getLias(authentication: String): HttpResponse

    suspend fun getCompanyProfile(authentication: String): HttpResponse

    suspend fun updateCompanyProfile(
        companyName: String,
        address: String,
        country: String,
        city: String,
        trnNumber: String,
        zipCode: String,
        checkIn: Boolean,
        businessType: String,
        businessLia: String,
        tradeLicenseNumber: String,
        timezoneOffset: String,
        timezoneName: String,
        authentication: String
    ): HttpResponse
}