package data.network.android.models

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep
import data.network.android.RoutineModel

@Keep
class GetSingleResponse() : Parcelable {
    var routine: RoutineModel = RoutineModel()
    var success: Boolean = false
    var time_ranges: ArrayList<TimeRoutineModel> = ArrayList()

    constructor(parcel: Parcel) : this() {
        routine = parcel.readParcelable(RoutineModel::class.java.classLoader)!!
        success = parcel.readByte() != 0.toByte()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeParcelable(routine, flags)
        parcel.writeByte(if (success) 1 else 0)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<GetSingleResponse> {
        override fun createFromParcel(parcel: Parcel): GetSingleResponse {
            return GetSingleResponse(parcel)
        }

        override fun newArray(size: Int): Array<GetSingleResponse?> {
            return arrayOfNulls(size)
        }
    }
}