package data.network.android

import core.locks.logs.models.LockLogActionType
import data.common.preferences.Preferences
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

fun LocksListResponse.LockModel.log(
    action: LockLogActionType,
    message: String,
    data: String = "",
) {
    CoroutineScope(Dispatchers.IO).launch {
        Preferences.lockLogger.log(
            actionType = action.toString(),
            lockInfoData = data,
            lockInternalId = internal_id,
            message = message,
            provider = provider,
            user = Preferences.userFullName.get()
        )
    }
}

fun GetAccessKeyModel.DataKeyModel.log(
    action: LockLogActionType,
    message: String,
    data: String = "",
) {
    CoroutineScope(Dispatchers.IO).launch {
        Preferences.lockLogger.log(
            actionType = action.toString(),
            lockInfoData = data,
            lockInternalId = internal_id,
            message = message,
            provider = provider,
            user = Preferences.userFullName.get()
        )
    }
}

fun Preferences.logLock(
    internalId: String,
    provider: String,
    action: LockLogActionType,
    message: String,
    data: String = "",
) {
    CoroutineScope(Dispatchers.IO).launch {
        lockLogger.log(
            actionType = action.toString(),
            lockInfoData = data,
            lockInternalId = internalId,
            message = message,
            provider = provider,
            user = Preferences.userFullName.get()
        )
    }
}