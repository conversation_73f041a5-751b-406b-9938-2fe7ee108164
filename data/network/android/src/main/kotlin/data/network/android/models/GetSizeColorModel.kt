package data.network.android.models

import androidx.annotation.Keep

@Keep
class GetSizeColorModel {

    var success: Boolean = false
    var lock_attributes: LockAttributesModel = LockAttributesModel()
}

@Keep
class LockAttributesModel {
    var Keyless: KeyLessModel = KeyLessModel()
    var ISEO: ISEOModel = ISEOModel()
}

@Keep
class ISEOModel {
    var colour: ArrayList<String> = ArrayList()
    var size: ArrayList<String> = ArrayList()
}

@Keep
class KeyLessModel {
    var colour: ArrayList<String> = ArrayList()
    var size: ArrayList<String> = ArrayList()
}