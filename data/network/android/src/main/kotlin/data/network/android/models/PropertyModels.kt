package data.network.android.models

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep
import com.google.android.gms.maps.model.LatLng

@Keep
class GetPropertyResponse() : Parcelable {
    var lock_count: Int = 0
    var message: String = ""
    var properties: ArrayList<Property> = ArrayList()
    var success: Boolean = false
    var status: Boolean = false

    constructor(parcel: Parcel) : this() {
        lock_count = parcel.readInt()
        message = parcel.readString()!!
        properties = parcel.createTypedArrayList(Property)!!
        success = parcel.readByte() != 0.toByte()
        status = parcel.readByte() != 0.toByte()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(lock_count)
        parcel.writeString(message)
        parcel.writeTypedList(properties)
        parcel.writeByte(if (success) 1 else 0)
        parcel.writeByte(if (status) 1 else 0)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<GetPropertyResponse> {
        override fun createFromParcel(parcel: Parcel): GetPropertyResponse {
            return GetPropertyResponse(parcel)
        }

        override fun newArray(size: Int): Array<GetPropertyResponse?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class Property() : Parcelable {
    var __v: Int = 0
    var _id: String = ""
    var area: String = ""
    var building_name: String = ""
    var created_at: String = ""
    var emirate: String = ""
    var latitude: String? = ""
    var longitude: String? = ""
    var manager_id: String? = ""
    var laundary_number: String? = ""
    var support_call_number: String = ""
    var support_whatsapp_number: String = ""
    var grocery_number: String? = ""
    var total_floors: String? = ""
    var icon_id: String? = ""
    var icon: ArrayList<IconModel>? = ArrayList()
    var total_locks: String = ""

    constructor(parcel: Parcel) : this() {
        __v = parcel.readInt()
        _id = parcel.readString()!!
        area = parcel.readString()!!
        building_name = parcel.readString()!!
        created_at = parcel.readString()!!
        emirate = parcel.readString()!!
        latitude = parcel.readString()
        longitude = parcel.readString()
        manager_id = parcel.readString()
        laundary_number = parcel.readString()
        support_call_number = parcel.readString()!!
        support_whatsapp_number = parcel.readString()!!
        grocery_number = parcel.readString()
        total_floors = parcel.readString()
        icon_id = parcel.readString()
        icon = parcel.createTypedArrayList(IconModel)!!
        total_locks = parcel.readString()!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(__v)
        parcel.writeString(_id)
        parcel.writeString(area)
        parcel.writeString(building_name)
        parcel.writeString(created_at)
        parcel.writeString(emirate)
        parcel.writeString(latitude)
        parcel.writeString(longitude)
        parcel.writeString(manager_id)
        parcel.writeString(laundary_number)
        parcel.writeString(support_call_number)
        parcel.writeString(support_whatsapp_number)
        parcel.writeString(grocery_number)
        parcel.writeString(total_floors)
        parcel.writeString(icon_id)
        parcel.writeTypedList(icon)
        parcel.writeString(total_locks)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<Property> {
        override fun createFromParcel(parcel: Parcel): Property {
            return Property(parcel)
        }

        override fun newArray(size: Int): Array<Property?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class IconModel() : Parcelable {
    var _id: String = ""
    var name: String = ""
    var icon: String = ""
    var type: String = ""
    var createdAt: String = ""

    constructor(parcel: Parcel) : this() {
        _id = parcel.readString()!!
        name = parcel.readString()!!
        icon = parcel.readString()!!
        type = parcel.readString()!!
        createdAt = parcel.readString()!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(_id)
        parcel.writeString(name)
        parcel.writeString(icon)
        parcel.writeString(type)
        parcel.writeString(createdAt)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<IconModel> {
        override fun createFromParcel(parcel: Parcel): IconModel {
            return IconModel(parcel)
        }

        override fun newArray(size: Int): Array<IconModel?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class SearchResultModel() : Parcelable {
    lateinit var latlng: LatLng
    var title: String = ""

    constructor(parcel: Parcel) : this() {
        latlng = parcel.readParcelable(LatLng::class.java.classLoader)!!
        title = parcel.readString()!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeParcelable(latlng, flags)
        parcel.writeString(title)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SearchResultModel> {
        override fun createFromParcel(parcel: Parcel): SearchResultModel {
            return SearchResultModel(parcel)
        }

        override fun newArray(size: Int): Array<SearchResultModel?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class SearchLocationResponse() : Parcelable {
    var predictions: ArrayList<Predictions> = ArrayList()
    var status: String = ""

    constructor(parcel: Parcel) : this() {
        predictions = parcel.createTypedArrayList(Predictions)!!
        status = parcel.readString()!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeTypedList(predictions)
        parcel.writeString(status)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SearchLocationResponse> {
        override fun createFromParcel(parcel: Parcel): SearchLocationResponse {
            return SearchLocationResponse(parcel)
        }

        override fun newArray(size: Int): Array<SearchLocationResponse?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class Predictions() : Parcelable {
    var description: String = ""
    var id: String = ""
    var place_id: String = ""
    var reference: String = ""
    var structured_formatting: StructuredFormatting = StructuredFormatting()

    constructor(parcel: Parcel) : this() {
        description = parcel.readString()!!
        id = parcel.readString()!!
        place_id = parcel.readString()!!
        reference = parcel.readString()!!
        structured_formatting = parcel.readParcelable(StructuredFormatting::class.java.classLoader)!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(description)
        parcel.writeString(id)
        parcel.writeString(place_id)
        parcel.writeString(reference)
        parcel.writeParcelable(structured_formatting, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<Predictions> {
        override fun createFromParcel(parcel: Parcel): Predictions {
            return Predictions(parcel)
        }

        override fun newArray(size: Int): Array<Predictions?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class StructuredFormatting() : Parcelable {
    var main_text: String = ""
    var secondary_text: String = ""

    constructor(parcel: Parcel) : this() {
        main_text = parcel.readString()!!
        secondary_text = parcel.readString()!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(main_text)
        parcel.writeString(secondary_text)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<StructuredFormatting> {
        override fun createFromParcel(parcel: Parcel): StructuredFormatting {
            return StructuredFormatting(parcel)
        }

        override fun newArray(size: Int): Array<StructuredFormatting?> {
            return arrayOfNulls(size)
        }
    }
}