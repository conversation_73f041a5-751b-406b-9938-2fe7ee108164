package data.network.android.models

import androidx.annotation.Keep

@Keep
class CreateRoutineRequest {

    var name: String = ""
    var always_open: Boolean? = false
    var time_ranges: ArrayList<TimeRoutineModel> = ArrayList()
}

@Keep
class TimeRoutineModel {
    var id: String = ""
    var _id: String = ""
    var allowed_days: ArrayList<Int> = ArrayList()
    var always_open: Boolean = false
    var time_slot: TimeSlot = TimeSlot()
    var end_hour: String = ""
    var end_min: String = ""
    var start_hour: String = ""
    var start_min: String = ""
    var holidays: Boolean = false
}

@Keep
class TimeSlot {
    var end_hour: String = ""
    var end_min: String = ""
    var start_hour: String = ""
    var start_min: String = ""
}