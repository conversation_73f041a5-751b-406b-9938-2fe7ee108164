package data.network.android.models

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

@Keep
class ManageStaffModel() : Parcelable {
    var success: Boolean = false
    var members: ArrayList<StaffMembersModel> = ArrayList()

    constructor(parcel: Parcel) : this() {
        success = parcel.readByte() != 0.toByte()
        members = parcel.createTypedArrayList(StaffMembersModel.CREATOR)!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeByte(if (success) 1 else 0)
        parcel.writeTypedList(members)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ManageStaffModel> {
        override fun createFromParcel(parcel: Parcel): ManageStaffModel {
            return ManageStaffModel(parcel)
        }

        override fun newArray(size: Int): Array<ManageStaffModel?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class StaffMembersModel() : Parcelable {
    var _id: String = ""
    var username: String = ""
    var status: String = ""
    var user_type: String = ""
    var integrator_type: String = ""
    var manager_id: String = ""
    var name: String = ""
    var profile_photo: String = ""
    var passport_number: String = ""
    var country_code: String = ""
    var mobile_number: String = ""
    var email: String = ""
    var dob: String = ""
    var uid: String = ""
    var support_call_number: String = ""
    var support_whatsapp_number: String = ""
    var walkthrough_status: String = ""
    var valid_from: String = ""
    var valid_to: String = ""
    var is_valid: Int = -1
    var valid_token: String = ""
    var created_at: String = ""
    var urn_id: String = ""
    var device_type: String = ""
    var device_token: String = ""
    var first_name: String = ""
    var last_name: String = ""
    var role_data: StaffMemberRoleModel = StaffMemberRoleModel()

    constructor(parcel: Parcel) : this() {
        _id = parcel.readString()!!
        username = parcel.readString()!!
        status = parcel.readString()!!
        user_type = parcel.readString()!!
        integrator_type = parcel.readString()!!
        manager_id = parcel.readString()!!
        name = parcel.readString()!!
        profile_photo = parcel.readString()!!
        passport_number = parcel.readString()!!
        country_code = parcel.readString()!!
        mobile_number = parcel.readString()!!
        email = parcel.readString()!!
        dob = parcel.readString()!!
        uid = parcel.readString()!!
        support_call_number = parcel.readString()!!
        support_whatsapp_number = parcel.readString()!!
        walkthrough_status = parcel.readString()!!
        valid_from = parcel.readString()!!
        valid_to = parcel.readString()!!
        is_valid = parcel.readInt()
        valid_token = parcel.readString()!!
        created_at = parcel.readString()!!
        urn_id = parcel.readString()!!
        device_type = parcel.readString()!!
        device_token = parcel.readString()!!
        first_name = parcel.readString()!!
        last_name = parcel.readString()!!
        role_data = parcel.readParcelable(StaffMemberRoleModel::class.java.classLoader)!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(_id)
        parcel.writeString(username)
        parcel.writeString(status)
        parcel.writeString(user_type)
        parcel.writeString(integrator_type)
        parcel.writeString(manager_id)
        parcel.writeString(name)
        parcel.writeString(profile_photo)
        parcel.writeString(passport_number)
        parcel.writeString(country_code)
        parcel.writeString(mobile_number)
        parcel.writeString(email)
        parcel.writeString(dob)
        parcel.writeString(uid)
        parcel.writeString(support_call_number)
        parcel.writeString(support_whatsapp_number)
        parcel.writeString(walkthrough_status)
        parcel.writeString(valid_from)
        parcel.writeString(valid_to)
        parcel.writeInt(is_valid)
        parcel.writeString(valid_token)
        parcel.writeString(created_at)
        parcel.writeString(urn_id)
        parcel.writeString(device_type)
        parcel.writeString(device_token)
        parcel.writeString(first_name)
        parcel.writeString(last_name)
        parcel.writeParcelable(role_data, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<StaffMembersModel> {
        override fun createFromParcel(parcel: Parcel): StaffMembersModel {
            return StaffMembersModel(parcel)
        }

        override fun newArray(size: Int): Array<StaffMembersModel?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class StaffMemberRoleModel() : Parcelable {
    var _id: String = ""
    var name: String = ""
    var display_name: String = ""
    var creator_type: String = ""
    var company_id: String = ""
    var created_at: String = ""

    constructor(parcel: Parcel) : this() {
        _id = parcel.readString()!!
        name = parcel.readString()!!
        display_name = parcel.readString()!!
        creator_type = parcel.readString()!!
        company_id = parcel.readString()!!
        created_at = parcel.readString()!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(_id)
        parcel.writeString(name)
        parcel.writeString(display_name)
        parcel.writeString(creator_type)
        parcel.writeString(company_id)
        parcel.writeString(created_at)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<StaffMemberRoleModel> {
        override fun createFromParcel(parcel: Parcel): StaffMemberRoleModel {
            return StaffMemberRoleModel(parcel)
        }

        override fun newArray(size: Int): Array<StaffMemberRoleModel?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class SeprateRoleApiModelMain() : Parcelable {

    var success: Boolean = false
    var roles: ArrayList<SeprateRoleApiModel> = ArrayList()

    constructor(parcel: Parcel) : this() {
        success = parcel.readByte() != 0.toByte()
        roles = parcel.createTypedArrayList(SeprateRoleApiModel.CREATOR)!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeByte(if (success) 1 else 0)
        parcel.writeTypedList(roles)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SeprateRoleApiModelMain> {
        override fun createFromParcel(parcel: Parcel): SeprateRoleApiModelMain {
            return SeprateRoleApiModelMain(parcel)
        }

        override fun newArray(size: Int): Array<SeprateRoleApiModelMain?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class SeprateRoleApiModel() : Parcelable {
    var role: RoleApiModel = RoleApiModel()

    constructor(parcel: Parcel) : this() {
        role = parcel.readParcelable(RoleApiModel::class.java.classLoader)!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeParcelable(role, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SeprateRoleApiModel> {
        override fun createFromParcel(parcel: Parcel): SeprateRoleApiModel {
            return SeprateRoleApiModel(parcel)
        }

        override fun newArray(size: Int): Array<SeprateRoleApiModel?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class RoleApiModel() : Parcelable {
    var _id: String = ""
    var display_name: String = ""

    constructor(parcel: Parcel) : this() {
        _id = parcel.readString()!!
        display_name = parcel.readString()!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(_id)
        parcel.writeString(display_name)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<RoleApiModel> {
        override fun createFromParcel(parcel: Parcel): RoleApiModel {
            return RoleApiModel(parcel)
        }

        override fun newArray(size: Int): Array<RoleApiModel?> {
            return arrayOfNulls(size)
        }
    }
}