package data.keyless.home

import kotlinx.serialization.Serializable

@Serializable
internal data class IconDto(
    val _id: String = "",
    val name: String = "",
    val icon: String = "",
    val type: String = "",
    val createdAt: String = ""
)

@Serializable
internal data class LockAssignmentDataDto(
    val __v: Int = 0,
    val _id: String = "",
    val assigned_at: String = "",
    val assigned_by: String = "",
    val assigned_to: String = "",
    val lock_id: String = "",
    val time_profile_id: TimeProfileIdDto = TimeProfileIdDto(),
    val valid_from: String = "",
    val valid_to: String = "",
    val status: Int = 0
)

@Serializable
internal data class LockAssignmentDto(
    val assignment_data: LockAssignmentDataDto = LockAssignmentDataDto(),
    val time_ranges: ArrayList<TimeRangeDto> = ArrayList()
)

@Serializable
internal data class LockDetailsDto(
    val assignment: LockAssignmentDto = LockAssignmentDto(),
    val lock: LockDto = LockDto(),
    val owner_id: String = "",
    val unit_id: String = "",
    val booking_number: String = "",
    val privacy: Boolean = false,
    val privacy_changed: Boolean = false,
    val companyCheckin: Boolean = false,
    val checkin: Boolean = false,
    val totalCheckins: Int = 0,
    val property_details: PropertyDetailsDto,
    val passcodeId: String = "",
    val passcode: String = ""
)

@Serializable
internal data class LockDto(
    val __v: Int = 0,
    val _id: String = "",
    val access_key: String = "",
    val alloted: Boolean = false,
    val battery_level: Int = -1,
    val createdAt: String = "",
    val image: String = "",
    val lock_uid: String = "",
    val name: String = "",
    val desc: String = "",
    val provider: String = "",
    val status: Int = -1,
    val unique_key: String = "",
    val unit_id: String = "",
    val messer_token: String = "",
    val time_zone: String = "",
    val internal_id: String = "",
    val encrypted_key: String = "",
    val privacy_mode: Boolean = false,
    val primary: Boolean = false,
    val privacy_permission: Boolean = false,
    val privacy_owner: Boolean = false,
    val icon: ArrayList<IconDto> = ArrayList(),
    val firmware_updated: Boolean = false,
    val firmwareVersion: String? = "",
    val firmwareAvailable: String? = "",
    val tedeeLockId: String? = ""
)

@Serializable
internal data class PropertyDto(
    val _id: String,
    val latitude: String = "",
    val longitude: String = "",
    val manager_id: String = "",
    val manager_type: String = "",
    val emirate: String = "",
    val area: String = "",
    val building_name: String,
    val total_floors: Long = 0,
    val created_at: String = "",
    val icon_id: String = "",
    val support_call_number: String = "",
    val support_whatsapp_number: String = "",
    val icon: ArrayList<IconDto> = ArrayList(),
    val count: Int = 0,
    val id: String = _id,
    val laundary_number: String = "",
    val grocery_number: String = "",
    val maintainance_number: String = ""
)

@Serializable
internal data class PropertyDetailsDto(
    val _id: String,
    val latitude: String = "",
    val longitude: String = "",
    val manager_id: String = "",
    val manager_type: String = "",
    val emirate: String = "",
    val area: String = "",
    val building_name: String,
    val total_floors: Long = 0,
    val created_at: String = "",
    val icon_id: String = "",
    val support_call_number: String = "",
    val support_whatsapp_number: String = "",
    val count: Int = 0,
    val id: String = _id,
    val floor: String = "",
    val name: String = "",
    val room_number: String = "",
    val laundary_number: String = "",
    val grocery_number: String = "",
    val maintainance_number: String = "",
    val appartment_number: String = "",
    val map_id: String = ""
)

@Serializable
internal data class TimeProfileIdDto(
    val __v: Int = 0,
    val _id: String = "",
    val created_at: String = "",
    val created_by: String = "",
    val iseo_id: Int = 0,
    val name: String = "",
    val status: Boolean = false
)

@Serializable
internal data class TimeRangeDto(
    val __v: Int = 0,
    val _id: String = "",
    val allowed_days: ArrayList<Int> = ArrayList(),
    val always_open: Boolean = false,
    val created_at: String = "",
    val holidays: Boolean = false,
    val name: String = "",
    val routine_id: String = "",
    val status: Boolean = false,
    val time_slot: TimeSlotDto = TimeSlotDto()
)

@Serializable
internal data class TimeSlotDto(
    val start_hour: Int = 0,
    val start_min: Int = 0,
    val end_hour: Int = 0,
    val end_min: Int = 0
)

@Serializable
internal data class UserHomeResponseDto(
    var locks: ArrayList<LockDetailsDto> = ArrayList(),
    var properties: ArrayList<PropertyDto> = ArrayList(),
    var success: Boolean = false,
    var message: String = "",
    var assignment_id: String = "",
    var logout: Boolean = false,
    var is_paid: Boolean = false,
    var totalUnreadNotification: Int = 0
)