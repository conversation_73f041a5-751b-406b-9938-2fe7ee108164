package data.encryption

import java.nio.charset.StandardCharsets
import java.security.DigestException
import java.security.MessageDigest
import java.util.Arrays
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

object Decryption {

    fun decrypt(encrypted: String, secretKey: String): String {
        val cipherData: ByteArray = Base64.getDecoder().decode(encrypted)
        val saltData = cipherData.copyOfRange(8, 16)
        val md5: MessageDigest = MessageDigest.getInstance("MD5")
        val keyAndIV = generateKeyAndIV(32, 16, saltData, secretKey.toByteArray(StandardCharsets.UTF_8), md5)
        val key = SecretKeySpec(keyAndIV[0], "AES")
        val iv = IvParameterSpec(keyAndIV[1])
        val encryptedCipher = cipherData.copyOfRange(16, cipherData.size)
        val aesCBC = Cipher.getInstance("AES/CBC/PKCS5Padding")

        aesCBC.init(Cipher.DECRYPT_MODE, key, iv)

        return String(aesCBC.doFinal(encryptedCipher), StandardCharsets.UTF_8)
    }

    private fun generateKeyAndIV(
        keyLength: Int,
        ivLength: Int,
        salt: ByteArray,
        password: ByteArray?,
        md: MessageDigest
    ): Array<ByteArray?> {
        val digestLength = md.digestLength
        val requiredLength = (keyLength + ivLength + digestLength - 1) / digestLength * digestLength
        val generatedData = ByteArray(requiredLength)
        var generatedLength = 0

        return try {
            md.reset()
            while (generatedLength < keyLength + ivLength) {
                if (generatedLength > 0) {
                    md.update(
                        generatedData,
                        generatedLength - digestLength,
                        digestLength
                    )
                }
                md.update(password)
                md.update(salt, 0, 8)
                md.digest(generatedData, generatedLength, digestLength)

                generatedLength += digestLength
            }

            val result = arrayOfNulls<ByteArray>(2)
            result[0] = generatedData.copyOfRange(0, keyLength)
            if (ivLength > 0) {
                result[1] = generatedData.copyOfRange(keyLength, keyLength + ivLength)
            }
            result
        } catch (e: DigestException) {
            throw java.lang.RuntimeException(e)
        } finally {
            Arrays.fill(generatedData, 0.toByte())
        }
    }
}