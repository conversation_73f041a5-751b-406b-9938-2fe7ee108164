<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    tools:context="feature.pm.addlock.CreateLockFragment">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

    <TextView
        android:id="@+id/textView2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:text="@string/create_lock"
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:padding="10dp"
        style="@style/ImageMirror"
        android:id="@+id/ivBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/iv_back_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.appcompat.widget.Toolbar>


    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar2"
        app:layout_constraintVertical_bias="0.0">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1.0">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cv_icon"
                android:theme="@style/Theme.MaterialComponents.Light"
                android:layout_marginTop="25dp"
                android:layout_width="82dp"
                android:layout_height="82dp"
                card_view:cardCornerRadius="10dp"
                card_view:cardElevation="0dp"
                android:layout_marginBottom="8dp"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                card_view:cardMaxElevation="0dp"
                card_view:strokeColor="@color/border_color"
                card_view:strokeWidth="1dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/ivIcon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_image_placeholder" />

            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:id="@+id/tv_press"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="center"
                android:text="@string/select_icon"
                android:textColor="@color/black"
                android:textSize="16sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/cv_icon" />


            <TextView
                android:id="@+id/tv_brand"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/brand"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_press" />

            <EditText
                android:id="@+id/et_brand"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="20dp"
                style="@style/mirrorText"
                android:hint="@string/brand"
                android:fontFamily="@font/poppins_medium_500"
                android:editable="false"
                android:background="@drawable/bg_edit_corners"
                android:includeFontPadding="false"
                android:textColor="@color/black"
                android:textSize="16dp"
                android:padding="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_brand"
                tools:ignore="Deprecated" />

            <TextView
                android:id="@+id/tv_lock_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/lock_name"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/et_brand" />

            <EditText
                android:id="@+id/et_lock_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="20dp"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/lock_name"
                android:singleLine="true"
                style="@style/mirrorText"
                android:includeFontPadding="false"
                android:inputType="textCapWords"
                android:padding="14dp"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_lock_name" />

            <TextView
                android:id="@+id/tv_building_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/building_name"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/et_lock_name" />

            <EditText
                android:id="@+id/spinner_building"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:minWidth="400dp"
                android:layout_marginTop="4dp"
                style="@style/mirrorText"
                android:layout_marginEnd="20dp"
                android:background="@drawable/bg_edit_corners"
                android:clickable="true"
                android:editable="false"
                android:focusable="false"
                android:singleLine="true"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/building_name"
                android:includeFontPadding="false"
                android:padding="14dp"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_building_name" />


            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:backgroundTint="@color/black"
                android:src="@drawable/ic_arrow_downward"
                app:layout_constraintBottom_toBottomOf="@+id/spinner_building"
                app:layout_constraintEnd_toEndOf="@+id/spinner_building"
                app:layout_constraintTop_toTopOf="@+id/spinner_building" />

            <TextView
                android:id="@+id/tv_floor_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/floor_number"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/spinner_building" />

            <EditText
                android:id="@+id/et_floor_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="20dp"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/floor_number"
                android:includeFontPadding="false"
                android:inputType="number"
                android:singleLine="true"
                style="@style/mirrorText"
                android:padding="14dp"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_floor_number" />

            <TextView
                android:id="@+id/tvUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/unit_number"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/et_floor_number" />

            <TextView
                android:id="@+id/clearUnitNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_medium_500"
                android:includeFontPadding="false"
                android:padding="4dp"
                android:visibility="gone"
                android:text="@string/clear"
                android:textColor="@color/blue"
                app:layout_constraintBottom_toBottomOf="@+id/tvUnit"
                app:layout_constraintEnd_toEndOf="@+id/et_floor_number"
                app:layout_constraintTop_toTopOf="@+id/tvUnit" />

            <EditText
                android:id="@+id/etUnitNumber"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/unit_number"
                android:includeFontPadding="false"
                android:inputType="text"
                android:clickable="true"
                android:editable="false"
                android:drawableEnd="@drawable/ic_arrow_downward"
                android:focusable="false"
                android:singleLine="true"
                style="@style/mirrorText"
                android:padding="14dp"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="@+id/et_floor_number"
                app:layout_constraintStart_toStartOf="@+id/et_floor_number"
                app:layout_constraintTop_toBottomOf="@+id/tvUnit" />



            <TextView
                android:id="@+id/tv_apartment_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/apartment_number_room_number"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etUnitNumber" />

            <EditText
                android:id="@+id/et_apartment_number"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/apartment_number_room_number"
                android:includeFontPadding="false"
                android:inputType="text"
                style="@style/mirrorText"
                android:padding="14dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="@+id/et_floor_number"
                app:layout_constraintStart_toStartOf="@+id/et_floor_number"
                app:layout_constraintTop_toBottomOf="@+id/tv_apartment_number" />


            <TextView
                android:id="@+id/tv_room_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_regular_400"
                android:text="Room Number"
                android:textColor="@color/black"
                android:visibility="gone"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="@+id/tv_apartment_number"
                app:layout_constraintTop_toBottomOf="@+id/et_apartment_number" />


            <EditText
                android:id="@+id/et_room_number"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="Room Number"
                android:visibility="gone"
                android:includeFontPadding="false"
                android:inputType="text"
                style="@style/mirrorText"
                android:padding="14dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="@+id/et_apartment_number"
                app:layout_constraintStart_toStartOf="@+id/tv_room_number"
                app:layout_constraintTop_toBottomOf="@+id/tv_room_number" />

            <TextView
                android:id="@+id/btnSaveEditDetails"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginStart="60dp"
                android:layout_marginTop="43dp"
                android:layout_marginEnd="60dp"
                android:layout_marginBottom="41dp"
                android:background="@drawable/bg_btn_round"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:text="@string/btnSave"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/et_room_number" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
    <!--    </androidx.constraintlayout.widget.ConstraintLayout>-->
</androidx.constraintlayout.widget.ConstraintLayout>