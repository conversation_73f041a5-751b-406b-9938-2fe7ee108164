package feature.pm.addlock

import android.Manifest
import android.annotation.SuppressLint
import android.app.Dialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.IntentSender
import android.content.pm.PackageManager
import android.graphics.Rect
import android.location.LocationManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.serializer.SerializerFeature
import com.google.android.gms.common.api.GoogleApiClient
import com.google.android.gms.common.api.PendingResult
import com.google.android.gms.common.api.Status
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsResult
import com.google.android.gms.location.LocationSettingsStatusCodes
import com.google.gson.JsonObject
import com.iseo.v364sdk.services.exception.V364SdkException
import com.iseo.v364sdk.services.mobilecredentialservice.IMobileCredentialService
import com.iseo.v364sdk.services.mobilecredentialservice.IMobileHermesCmdService
import com.iseo.v364sdk.services.mobilecredentialservice.model.ILock
import com.iseo.v364sdk.services.mobilecredentialservice.model.IMobileCredentialsInfo
import com.iseo.v364sdk.services.mobilecredentialservice.model.user.ILockBatteryStatus
import com.iseo.v364sdk.services.mobilecredentialservice.model.user.ILockResponse
import com.iseo.v364sdk.services.scanservice.IScanManagerService
import com.iseo.v364sdk.services.scanservice.model.IKeyScanInfo
import com.iseo.v364sdk.services.scanservice.model.ILegacyScanInfo
import com.iseo.v364sdk.services.scanservice.model.ILockScanInfo
import com.iseo.v364sdk.services.scanservice.model.IMobileCredentialScanInfo
import com.iseo.v364sdk.services.scanservice.model.IScanBTManagerEvent
import com.messerschmitt.mstblelib.MSTBleInterface
import com.messerschmitt.mstblelib.MSTBleUtils
import com.messerschmitt.mstblelib.MSTsmartkey
import com.ttlock.bl.sdk.api.ExtendedBluetoothDevice
import com.ttlock.bl.sdk.api.TTLockClient
import com.ttlock.bl.sdk.callback.ControlLockCallback
import com.ttlock.bl.sdk.callback.ScanLockCallback
import com.ttlock.bl.sdk.constant.ControlAction
import com.ttlock.bl.sdk.entity.ControlLockResult
import com.ttlock.bl.sdk.entity.LockError
import com.ttlock.bl.sdk.util.LogUtil
import core.locks.logs.models.LockLogActionType
import core.lock.airbnk.AirBnkManager
import core.lock.airbnk.AndroidAirBnkManager
import core.lock.common.models.LockBrand
import core.locks.manager.LocksManager
import data.common.preferences.Preferences
import data.lock.common.lock.repositories.LocksRepository
import data.lock.common.lock.usecases.resetIseoTime
import data.lock.common.lock.usecases.resetTime
import data.network.android.GetAccessKeyModel
import data.network.android.log
import data.network.android.models.ModelAdminInstaller
import data.utils.android.CommonValues
import data.utils.android.applications.ServiceProvider
import data.utils.android.common.BleLockScanData
import data.utils.android.common.FileUtils
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import domain.common.ErrorHandler
import dots.animation.textview.TextAndAnimationView
import feature.common.dialogs.appToast
import feature.common.navigation.logoutAndNavToDashboard
import keyless.feature.pm.addlock.R
import keyless.feature.settings.maintenance.databinding.FragmentUnlockBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Clock
import org.koin.android.ext.android.inject
import rayo.logicsdk.bean.DSTClass
import rayo.logicsdk.bean.LockCodeClass
import rayo.logicsdk.bean.LockTimeClass
import rayo.logicsdk.ble.BleScanCallback
import rayo.logicsdk.ble.BluetoothLeScan
import rayo.logicsdk.data.LockBasicInfo
import rayo.logicsdk.data.LockBleOpenData
import rayo.logicsdk.sdk.BleLockSdk
import rayo.logicsdk.sdk.BleLockSdkCallback
import rayo.logicsdk.sdk.ResultBean
import rayo.logicsdk.utils.TimeUtils
import java.io.UnsupportedEncodingException
import java.nio.charset.StandardCharsets
import java.sql.Timestamp
import java.util.Calendar
import kotlin.coroutines.cancellation.CancellationException

class UnlockFragment : Fragment(), IScanBTManagerEvent {

    private var whichPlace: String = ""
    private var REQUEST_CHECK_SETTINGS = 3
    private var running: Boolean = false
    private lateinit var scanLockData: GetAccessKeyModel
    private var mBleLockSdk: BleLockSdk? = null
    private var mBluetoothManager: BluetoothManager? = null
    private var mBleName: MutableList<String?> = ArrayList<String?>()
    private var mBluetoothLeScan: BluetoothLeScan? = null
    private var mBleHandler: BleHandler? = null
    private var isScan = false
    private var mBluetoothDeviceHashMap: HashMap<String, BleLockScanData>? = null
    private var mBluetoothDevice: BleLockScanData? = null
    private var mMac: String? = null
    var autCase = "0"
    lateinit var mViewModel: UnlockFragmentViewModel
    private var argumentList: ModelAdminInstaller.DataModelInstaller =
        ModelAdminInstaller.DataModelInstaller()
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            requireActivity()
        )
    }
    private lateinit var scanManagerService: IScanManagerService
    private lateinit var mobileCredentialService: IMobileCredentialService
    private lateinit var mobileCredentialHermesCmdService: IMobileHermesCmdService
    private var lock: ILockScanInfo? = null
    private var info: IMobileCredentialScanInfo? = null
    private var iseoLock: ILock? = null
    private var DEVUID = ""
    private var decryptedAccessKey = ""
    private var mBleUtils: MSTBleUtils? = null
    private var mstBleInterface: MSTBleInterface? = null
    private var mstSmartkey: MSTsmartkey? = null
    lateinit var dialogLock: Dialog
    private lateinit var binding: FragmentUnlockBinding

    private lateinit var resetTimeOpenLockData: LockBleOpenData

    private val locksManager: LocksManager by inject()
    private val locksRepository: LocksRepository by inject()

    private val airbnk: AirBnkManager by inject()
    private val handler: ErrorHandler by inject()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        (airbnk as? AndroidAirBnkManager)?.bind(this)
        binding = FragmentUnlockBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initz()
        apiChecking()
        setOnClickListener()
    }

    private fun apiChecking() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("uid", sharePrefs.uuid)
        jsonObject.addProperty("is_admin", Preferences.isAdminLogin())
        mViewModel.getCheckUser(sharePrefs.token, jsonObject).observe(requireActivity()) {
            Preferences.timeZoneName.set(it.timezone_name)
            Preferences.timeZoneOffset.set(it.timezone)
            if (!it.success) {
                logoutAndNavToDashboard(
                    it.message,
                    requireActivity()
                )
            }
        }
    }

    private fun apiImplementation() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("lock_id", scanLockData.data.unique_key)
        jsonObject.addProperty("uid", sharePrefs.uuid)
        mViewModel
            .assign_lock_iseo(sharePrefs.token, Preferences.userRole.get(), argumentList, jsonObject)
            .observe(requireActivity()) {
                refreshCredentials()
            }
    }

    private fun refreshCredentials() {
        scanManagerService = ServiceProvider.scanManagerService
        mobileCredentialService = ServiceProvider.mobileCredentialService
        CoroutineScope(Dispatchers.Main).launch {
//            updateStateNeutral("REFRESHING")
            delay(500)
            withContext(Dispatchers.IO) {
                try {
//                    val url = "https://sirademo.iseov364.com"
                    mobileCredentialService.refreshMobileCredential(
                        sharePrefs.iseoUrl,
                        sharePrefs.plantName
                    )
                    val mobileCredentialInfo: IMobileCredentialsInfo =
                        mobileCredentialService.getMobileCredentials(sharePrefs.iseoUrl)

                    val message = "ResCode:" + mobileCredentialInfo.resCode + "\n" +
                            "ResultDetails:" + mobileCredentialInfo.resultDetails + "\n" +
                            "ValidityStart:" + mobileCredentialInfo.validityStart + "\n" +
                            "ValidityEnd:" + mobileCredentialInfo.validityEnd + "\n" +
                            "ValidationEnd:" + mobileCredentialInfo.validationEnd + "\n" +
                            "Credential count:" + mobileCredentialInfo.mobileCredentials.size
                    withContext(Dispatchers.Main) {
                        Log.e("REFRESH SUCCESS\n$message", "")
                    }
                    scanLockData.data.log(
                        action = LockLogActionType.AddLock,
                        message = "Access Denied!!",
                        data = ""

                    )

//                    getSharedPreferences("prefs", Context.MODE_PRIVATE).edit().putString("url", url).apply()
                } catch (e: V364SdkException) {
                    withContext(Dispatchers.Main) {
                        scanLockData.data.log(
                            action = LockLogActionType.AddLock,
                            message = "ISEO Credential is not valid",
                            data = e.toString()
                        )
//                        updateState(e.v364SdkErrorCode)
                    }
//                    Log.e(TAG, "startService: ", e)
                }
            }
        }
    }

    private fun initLock() {
        mBleName = ArrayList<String?>()
        mBleName.add(CommonValues.EXTRA_C_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_D_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_B_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_E_BLE_NAME)
        mBluetoothLeScan =
            object : BluetoothLeScan(BluetoothAdapter.getDefaultAdapter(), 0, mBleScanCallback) {
                override fun enableBluetooth(): Boolean {
                    return true
                }
            }
        mBleHandler = BleHandler()
        isScan = false
        mBluetoothDeviceHashMap = HashMap()
        Handler().postDelayed({
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
            mBluetoothDeviceHashMap = HashMap()
            mBluetoothLeScan!!.startReceiver()
            isScan = true
        }, 1000)
    }

    override fun onStop() {
        super.onStop()
        LocalBroadcastManager.getInstance(requireActivity())
            .unregisterReceiver(MSTBleStatusChangeReceiver)
    }

    @SuppressLint("HandlerLeak")
    private inner class BleHandler : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                CommonValues.ADD_ADAPTER -> {
                    if (msg.obj != null) {
                        addAdapterItemRange(msg.obj as BleLockScanData)
                    }
                }

                else -> {
                }
            }
        }
    }

    private fun addAdapterItemRange(bluetoothDevice: BleLockScanData) {
        if (mBleName.size == 0 || checkName(bluetoothDevice.bleName)) {
            if (!mBluetoothDeviceHashMap!!.containsKey(bluetoothDevice.bleMac)) {
                addItemIn(bluetoothDevice)
                mBluetoothDeviceHashMap!![bluetoothDevice.bleMac] = bluetoothDevice
            }
        }
    }

    private fun addItemIn(bluetoothDevice: BleLockScanData) {
        if (bluetoothDevice == null) {
            return
        }

        mBluetoothDevice = bluetoothDevice
        if (mBluetoothDevice!!.bleName.contains(scanLockData.data.unique_key)) {
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
//            start.cancel()
            initData()
        } else {
            isScan = true
        }
    }

    private fun initData() {
        mMac = mBluetoothDevice?.bleMac
        mBleLockSdk = BleLockSdk()
        mBluetoothManager =
            requireActivity().getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager?
        mBleLockSdk?.init(mBluetoothManager, mBleLockSdkCallback)
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({
            val config = FileUtils.readStringFromFile(requireActivity())
            var lockCodeClass = JSON.parseObject(
                config,
                LockCodeClass::class.java
            )
            if (null == lockCodeClass) {
                lockCodeClass = LockCodeClass()
            }

            lockCodeClass = LockCodeClass()
            lockCodeClass.ivCode = "8qli5ljbkBJSBip8".toByteArray()
            lockCodeClass.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
            lockCodeClass.sysCode = decryptedAccessKey.toByteArray()
            mBleLockSdk?.connect(
                lockCodeClass,
                mBluetoothManager,
                context,
                mBluetoothDevice?.bleMac,
                mBluetoothDevice?.scanRecord,
                "1".toByteArray(),
                null,
                false
            )
        }, 10)
    }

    private fun checkName(bleName: String): Boolean {
        if (null == bleName) return false
        for (ble in bleName) {
            if (bleName.contains(ble)) {
                return true
            }
        }
        return false
    }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[UnlockFragmentViewModel::class.java]
        scanLockData = arguments?.getParcelable("scanLockData")!!
        whichPlace = arguments?.getString("whichPlace")!!
        argumentList = arguments?.getParcelable("list")!!
        binding.tvLockType.text = scanLockData.data.provider
        if (scanLockData.data.provider == CommonValues.iseo) {
            apiImplementation()
        }


        if (scanLockData.data.encrypted_key.isNotEmpty()) {
            val decryptedKey =
                CommonValues.decrypt(scanLockData.data.encrypted_key, sharePrefs.uuid)
            if (decryptedKey == null) {
                decryptedAccessKey = ""
            } else if (scanLockData.data.encrypted_key.isNotEmpty()) {
                decryptedAccessKey = decryptedKey!!
            } else {
                scanLockData.data.log(
                    action = LockLogActionType.AddLock,
                    message = "Encrypted key not found",
                    data = ""
                )
            }
//            else {
//                if (scanLockData.data.provider == CommonValues.keyless){
//                    decryptedAccessKey = scanLockData.data.access_key
//                }else if (scanLockData.data.provider == CommonValues.iseo){
//                    decryptedAccessKey = scanLockData.data.messer_token
//                }
//            }
        }


        DEVUID = scanLockData.data.lock_uid

    }

    private fun setOnClickListener() {
        binding.btnUnlock.setOnClickListener {
            askPermission()
        }
    }

    private fun askPermission() {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN
                ),
                50
            )
        } else {
            requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION,
                ),
                50
            )
        }
    }

    private fun commonInit() {
        showDialogForScanning()
//        progressLay.isVisible = true
        running = false
        when (scanLockData.data.provider) {
            CommonValues.iseo -> {
                scanManagerService = ServiceProvider.scanManagerService
                mobileCredentialService = ServiceProvider.mobileCredentialService;
                mobileCredentialHermesCmdService =
                    ServiceProvider.mobileCredentialHermesCmdService
                scanManagerService.setScanBTManagerEvent(this)
                scanISEOLock()
            }

            CommonValues.messerSchimtt -> {
                try {
                    loadSmartkey()
                } catch (e: UnsupportedEncodingException) {
                    e.printStackTrace()
                }
                initMesserSchmit()
            }

            CommonValues.keyless -> {
                initLock()
            }

            CommonValues.oji, CommonValues.linko -> {
                initOji()
            }

            CommonValues.lockWise -> {
                lifecycleScope.launch {
                    handler.async(
                        onError = { ex -> context?.appToast(ex.message ?: "Error happened") },
                        work = { initLockWise() }
                    )
                }
            }
        }
    }

    private fun initOji() {
        TTLockClient.getDefault().startScanLock(object : ScanLockCallback {
            override fun onScanLockSuccess(device: ExtendedBluetoothDevice?) {
                if (scanLockData.data.unique_key.contains(device!!.name) || device.name.contains(scanLockData.data.unique_key)) {
                    findLockDone()
                    TTLockClient.getDefault().stopScanLock()
                    scanLockData.data.log(
                        action = LockLogActionType.ScanLock,
                        message = "Scan Lock Successfully!",
                        data = decryptedAccessKey
                    )
                }
            }

            override fun onFail(error: LockError?) {
                scanLockData.data.log(
                    action = LockLogActionType.ScanLock,
                    message = error.toString(),
                    data = decryptedAccessKey
                )
            }
        })

    }

    private fun findLockDone() {

        TTLockClient.getDefault()
            .controlLock(ControlAction.UNLOCK, decryptedAccessKey, object : ControlLockCallback {
                override fun onControlLockSuccess(controlLockResult: ControlLockResult) {
                    dialogLock.dismiss()
                    val bundle = Bundle()
                    bundle.putParcelable("scanLockModel", scanLockData.data)
                    bundle.putString("whichPlace", whichPlace)
                    bundle.putParcelable("list", argumentList)
                    CommonValues.loadFragment(
                        CreateLockFragment(), bundle, parentFragmentManager,
                        R.id.frameContainerAddLock
                    )

                    mViewModel.postBatteryPercentage(
                        sharePrefs.token,
                        controlLockResult.battery, scanLockData.data.internal_id
                    )

                    scanLockData.data.log(
                        action = LockLogActionType.Unlock,
                        message = "Unlock Successfully!",
                        data = decryptedAccessKey
                    )
                }

                override fun onFail(error: LockError) {
                    LogUtil.d("lockAction:$error")
                    scanLockData.data.log(
                        action = LockLogActionType.UnlockError,
                        message = error.toString(),
                        data = decryptedAccessKey
                    )
                }
            })


    }

    private fun showDialogForScanning() {
        dialogLock = Dialog(requireActivity())
        dialogLock.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialogLock.window?.setBackgroundDrawableResource(keyless.feature.common.R.drawable.white_all_corners_10)
        dialogLock.setCancelable(false)
        dialogLock.setContentView(keyless.feature.common.R.layout.lock_scanning_dialog)
        var animatedDots = dialogLock.findViewById<TextAndAnimationView>(
            keyless.feature.common.R.id.animatedDotsDialog
        )
        var cancelBtn = dialogLock.findViewById<TextView>(keyless.feature.common.R.id.cancelBtn)
        animatedDots.animate()
        dialogLock.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialogLock.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
            val maxWidth = displayRectangle.width() * 0.8f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight)

        }
        cancelBtn.setOnClickListener {
            mBleScanCallback.finishScan()
            mBleLockSdk?.disconnect()
            dialogLock.dismiss()
        }
        dialogLock.show()

    }

    private fun initMesserSchmit() {
        mBleUtils = MSTBleUtils(requireActivity(), -85, -75)
        mstBleInterface = MSTBleInterface(mBleUtils, requireActivity(), mstSmartkey)

        val mIsBluetoothOn: Boolean = mBleUtils!!.isBluetoothOn
        val mIsBluetoothLePresent: Boolean = mBleUtils!!.isBluetoothLeSupported

        mBleUtils!!.askUserToEnableBluetoothIfNeeded()
        if (mIsBluetoothOn && mIsBluetoothLePresent) {
            LocalBroadcastManager.getInstance(requireActivity()).registerReceiver(
                MSTBleStatusChangeReceiver,
                makeGattUpdateIntentFilter()!!
            )
            mstBleInterface!!.mstOpenDoor(scanLockData.data.unique_key)
        } else {
            requireActivity().toast(
                getString(
                    keyless.data.utils.android.R.string.bluetooth_not_enabled_by_user_or_it_will_not_support_by_device
                )
            )
        }

    }

    private fun makeGattUpdateIntentFilter(): IntentFilter? {
        val intentFilter = IntentFilter()
        intentFilter.addAction(MSTBleInterface.ACTION_START_OPEN)
        intentFilter.addAction(MSTBleInterface.ACTION_SCAN_TIMEOUT)
        intentFilter.addAction(MSTBleInterface.ACTION_CONNECT_TO)
        intentFilter.addAction(MSTBleInterface.ACTION_SPECIAL_NAME)
        intentFilter.addAction(MSTBleInterface.ACTION_OPEN_THE_DOOR)
        intentFilter.addAction(MSTBleInterface.ACTION_OPEN_THE_DOOR_FAILED)
        intentFilter.addAction(MSTBleInterface.ACTION_FINISH_OPEN)
        intentFilter.addAction(MSTBleInterface.ERROR_DEVICE_RESPONSE)
        intentFilter.addAction(MSTBleInterface.ERROR_DISCOVER_SERVICES)
        intentFilter.addAction(MSTBleInterface.ERROR_CONNECT)
        intentFilter.addAction(MSTBleInterface.ERROR_DOOR_STATE)
        intentFilter.addAction(MSTBleInterface.ERROR_KEY_RESULT)
        intentFilter.addAction(MSTBleInterface.ERROR_ENABLE_NOTIFY)
        intentFilter.addAction(MSTBleInterface.ERROR_AUTH_FAILED)
        return intentFilter
    }


    private val MSTBleStatusChangeReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            val action2 = intent.getBooleanExtra("dataBattery", false)
//            val action1 = intent.getParcelableExtra<MSTBleDevice>("data")
//            if (action1 != null) {
//                repeatUnLock = action1
//            }
            //*********************
            if (action == MSTBleInterface.ACTION_START_OPEN) {

            }
            //*********************
            if (action == MSTBleInterface.ACTION_SCAN_TIMEOUT) {

            }
            //*********************
            if (action == MSTBleInterface.ACTION_CONNECT_TO) {
                mstBleInterface!!.openDoorAddLock()
                scanLockData.data.log(
                    action = LockLogActionType.AddLock,
                    message = "Connected",
                    data = ""
                )
            }


            if (action == MSTBleInterface.ACTION_BATT_STATUS) {
                Log.e("// battery Status cddhgdty $action2", "")

                var batteryCount = 1000
                if (!action2) {
                    batteryCount = 1
                } else if (action2) {
                    batteryCount = 3
                }
                mViewModel.postBatteryPercentage(
                    sharePrefs.token,
                    batteryCount, scanLockData.data.internal_id
                )

                scanLockData.data.log(
                    action = LockLogActionType.Unlock,
                    message = "Battery Status",
                    data = batteryCount.toString()
                )
            }

            //*********************
            if (action == MSTBleInterface.ACTION_SPECIAL_NAME) {
            }
            //*********************
            if (action == MSTBleInterface.ACTION_OPEN_THE_DOOR) {


            }
            //*********************
            if (action == MSTBleInterface.ACTION_OPEN_THE_DOOR_FAILED) {
                scanLockData.data.log(
                    action = LockLogActionType.AddLock,
                    message = "Door open failed",
                    data = ""
                )

            }
            //*********************
            if (action == MSTBleInterface.ACTION_FINISH_OPEN) {
                dialogLock.dismiss()
                val bundle = Bundle()
                bundle.putParcelable("scanLockModel", scanLockData.data)
                bundle.putString("whichPlace", whichPlace)
                bundle.putParcelable("list", argumentList)
                CommonValues.loadFragment(
                    CreateLockFragment(), bundle, fragmentManager!!,
                    R.id.frameContainerAddLock
                )
                scanLockData.data.log(
                    action = LockLogActionType.AddLock,
                    message = "Open Successfully",
                    data = ""
                )
            }
            //*********************
            if (action == MSTBleInterface.ERROR_CONNECT) {
                scanLockData.data.log(
                    action = LockLogActionType.AddLock,
                    message = "Error while connecting",
                    data = ""
                )
            }
            //*********************
            if (action == MSTBleInterface.ERROR_DEVICE_RESPONSE) {
            }
            //*********************
            if (action == MSTBleInterface.ERROR_DISCOVER_SERVICES) {

            }
            //*********************
            if (action == MSTBleInterface.ERROR_DOOR_STATE) {
            }
            //*********************
            if (action == MSTBleInterface.ERROR_ENABLE_NOTIFY) {

            }
            //*********************
            if (action == MSTBleInterface.ERROR_KEY_RESULT) {
            }
            //*********************
            if (action == MSTBleInterface.ERROR_AUTH_FAILED) {

            }
        }
    }

    private fun loadSmartkey() {
        mstSmartkey = MSTsmartkey(
            decryptedAccessKey,
            DEVUID.toByteArray(
                StandardCharsets.UTF_8
            )
        )
    }


    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (
                    grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED && grantResults[1] == PackageManager.PERMISSION_GRANTED) {
                    if (CommonValues.isBluetoothEnabled()) {
                        commonInit()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                } else {
                    showDialogForPermissions()
                }
            } else {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {

                    val lm = requireActivity().getSystemService(AppCompatActivity.LOCATION_SERVICE) as LocationManager
                    var gps_enabled = false
                    var network_enabled = false

                    try {
                        gps_enabled = lm.isProviderEnabled(LocationManager.GPS_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    try {
                        network_enabled = lm.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    if (!gps_enabled && !network_enabled) {
                        displayLocationSettingsRequest()
                    } else {
                        if (CommonValues.isBluetoothEnabled()) {
                            commonInit()
                        } else {
                            val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                            if (!mBluetoothAdapter.isEnabled) {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                            } else {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                            }
                        }
                    }
                }
            }
        }
    }

    private fun displayLocationSettingsRequest() {
        val googleApiClient = GoogleApiClient.Builder(requireActivity())
            .addApi(LocationServices.API).build()
        googleApiClient.connect()
        val locationRequest: LocationRequest = LocationRequest.create()
        locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        locationRequest.interval = 10000
        locationRequest.fastestInterval = 10000 / 2
        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
        builder.setAlwaysShow(true)
        val result: PendingResult<LocationSettingsResult> =
            LocationServices.SettingsApi.checkLocationSettings(googleApiClient, builder.build())
        result.setResultCallback { result ->
            val status: Status = result.status
            when (status.statusCode) {
                LocationSettingsStatusCodes.SUCCESS -> {
                    if (CommonValues.isBluetoothEnabled()) {
                        commonInit()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                }

                LocationSettingsStatusCodes.RESOLUTION_REQUIRED -> {
                    try {
                        startIntentSenderForResult(
                            status.resolution!!.intentSender,
                            REQUEST_CHECK_SETTINGS,
                            null,
                            0,
                            0,
                            0,
                            null
                        );
                    } catch (e: IntentSender.SendIntentException) {
                    }
                }

                LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE -> {}
            }
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10) {
            askPermission()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode != 0) {
            commonInit()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode == 0) {
            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
        } else if (requestCode == REQUEST_CHECK_SETTINGS) {
            displayLocationSettingsRequest()
        }
    }

    private fun showDialogForPermissions() {
        defaultDialog(
            requireActivity(),
            getString(keyless.data.utils.android.R.string.please_allow_bluetooth),
            object : OnActionOK {
                override fun onClickData() {
                    if (activity != null && isAdded) {
                        startActivityForResult(
                            Intent(
                                Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                Uri.fromParts("package", requireActivity().packageName, null),
                            ), 10
                        )
                    }
                }
            })
    }

    private fun scanISEOLock() {
        CoroutineScope(Dispatchers.Main).launch {
            withContext(Dispatchers.IO) {
                scanManagerService.stopScan()
                try {
                    scanManagerService.startScanLock(false)
                } catch (e: V364SdkException) {

                }
            }
        }
        scanLockData.data.log(
            action = LockLogActionType.AddLock,
            message = "Scanning Lock",
            data = ""
        )

    }


    private val mBleScanCallback: BleScanCallback = object : BleScanCallback {

        @SuppressLint("MissingPermission")
        override fun findBle(bluetoothDevice: BluetoothDevice, rssi: Int, scanRecord: ByteArray) {
            val bleLockScanData = BleLockScanData(
                bluetoothDevice.name ?: "",
                bluetoothDevice.address ?: "",
                scanRecord
            )
            mBleHandler!!.obtainMessage(CommonValues.ADD_ADAPTER, bleLockScanData).sendToTarget()
        }

        override fun finishScan() {}
    }

    private val mBleLockSdkCallback: BleLockSdkCallback = object : BleLockSdkCallback {
        override fun init(resultBean: ResultBean<*>?) {
            rayo.logicsdk.utils.Log.d(
                "TAG",
                JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )
        }

        override fun connect(resultBean: ResultBean<*>) {
            scanLockData.data.log(
                action = LockLogActionType.AddLock,
                message = "Connected",
                data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )
        }

        override fun authentication(resultBean: ResultBean<*>) {
            if (resultBean.isRet) {
                val lockBleOpenData = LockBleOpenData()
                val nowTime = Calendar.getInstance()
                nowTime.add(Calendar.MINUTE, -1)
                lockBleOpenData.beginTime = TimeUtils.dateFromNotYMDHMS(
                    TimeUtils.dateToNotYMDHMS(nowTime.time).toString().trim { it <= ' ' })
                nowTime.add(Calendar.HOUR_OF_DAY, 1)
                lockBleOpenData.endTime = TimeUtils.dateFromNotYMDHMS(
                    TimeUtils.dateToNotYMDHMS(nowTime.time).toString().trim { it <= ' ' })

                val mLockBasicInfo = resultBean.obj as (LockBasicInfo)

                var batteryCount = 1000
                if (mLockBasicInfo.battery in 0..25) {
                    batteryCount = 0
                } else if (mLockBasicInfo.battery in 26..50) {
                    batteryCount = 1
                } else if (mLockBasicInfo.battery in 51..75) {
                    batteryCount = 2
                } else if (mLockBasicInfo.battery > 75) {
                    batteryCount = 3
                }

                mViewModel.postBatteryPercentage(
                    sharePrefs.token,
                    batteryCount, scanLockData.data.internal_id
                )

                scanLockData.data.log(
                    action = LockLogActionType.AddLock,
                    message = "Authentication Successful",
                    data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
                )
                resetTimeOpenLockData = lockBleOpenData
                val lockTimeClass = LockTimeClass()
                val timestamp = Timestamp(System.currentTimeMillis())
                lockTimeClass.lockTime = timestamp
                lockTimeClass.dstClass = DSTClass()
                mBleLockSdk?.setLockTime(lockTimeClass)
            }
//            else {
//                autCase = "2"
//                var msg = ""
//                msg = if (JSON.toJSONString(
//                        resultBean,
//                        SerializerFeature.WriteDateUseDateFormat
//                    ).toLowerCase().contains("timeout")
//                ) {
//                    getString(R.string.the_lock_could_not_be_connected)
//                } else {
//                    JSON.toJSONString(
//                        resultBean,
//                        SerializerFeature.WriteDateUseDateFormat
//                    )
//                }
//                defaultDialog(requireActivity(), msg, object : OnActionOK {
//                    override fun onClickData() {
//
//                    }
//                })
//                CommonValues.saveLockLogs(
//                    scanLockData.data.provider,
//                    scanLockData.data.internal_id,
//                    "Timeout",
//                    "Add Lock",
//                    JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat),
//                    requireActivity()
//                )
//
//                dialogLock.dismiss()
////                progressLay.isVisible = false
//            }
        }

        override fun disConnect(resultBean: ResultBean<*>?) {
            if (activity != null && isAdded) {
                scanLockData.data.log(
                    action = LockLogActionType.AddLock,
                    message = "Disconnect",
                    data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
                )
            }

        }

        override fun registerLock(resultBean: ResultBean<*>) {
            if (resultBean.isRet) {
            }
        }

        override fun getLockInfo(resultBean: ResultBean<*>) {

        }

        override fun setLockInfo(resultBean: ResultBean<*>?) {

        }

        override fun setLockTime(resultBean: ResultBean<*>?) {
            if (resultBean?.isRet == true) {
                mBleLockSdk?.bleOpenLock(resetTimeOpenLockData)
                autCase = "1"
            } else {
                if (::dialogLock.isInitialized) {
                    dialogLock.dismiss()
                }
            }
        }

        override fun getLockOfficeMode(resultBean: ResultBean<*>) {

        }

        override fun setLockOfficeMode(resultBean: ResultBean<*>) {

        }

        override fun getLockHotPoint(resultBean: ResultBean<*>?) {

        }

        override fun setLockHotPoint(resultBean: ResultBean<*>?) {

        }

        override fun checkLockHotPoint(resultBean: ResultBean<*>?) {

        }

        override fun getLockStatus(resultBean: ResultBean<*>?) {

        }

        override fun readLockEvent(resultBean: ResultBean<*>?) {

        }

        override fun cleanLockEvent(resultBean: ResultBean<*>?) {

        }

        override fun updateLockPermission(resultBean: ResultBean<*>?) {

        }

        override fun findLockPermission(resultBean: ResultBean<*>?) {

        }

        override fun cleanLockPermission(resultBean: ResultBean<*>?) {

        }

        override fun bleOpenLock(resultBean: ResultBean<*>) {

            if (resultBean.isRet) {
                mBluetoothLeScan!!.stopReceiver()
                mBleLockSdk?.disconnect()
                dialogLock.dismiss()
                val bundle = Bundle()
                bundle.putParcelable("scanLockModel", scanLockData.data)
                bundle.putString("whichPlace", whichPlace)
                bundle.putParcelable("list", argumentList)
                CommonValues.loadFragment(
                    CreateLockFragment(), bundle, parentFragmentManager,
                    R.id.frameContainerAddLock
                )

//                showConfirmationDialog()
            } else {
                mBluetoothLeScan!!.stopReceiver()
                mBleLockSdk?.disconnect()
//                defaultDialog(
//                    requireActivity(),
//                    getString(R.string.scanned_lock_could_not_found),
//                    object : OnActionOK {
//                        override fun onClickData() {
//                            mBluetoothLeScan!!.stopReceiver()
//                            startActivity(
//                                Intent(
//                                    requireActivity(),
//                                    AddLockMainActivity::class.java
//                                ).putExtra("finishActivity", "finishActivity")
//                            )
//                        }
//                    })
                scanLockData.data.log(
                    action = LockLogActionType.AddLock,
                    message = "Timeout Error",
                    data = ""
                )

            }
        }

        override fun pincodeOpenLock(resultBean: ResultBean<*>?) {

        }

        override fun findLockBlacklist(resultBean: ResultBean<*>?) {

        }

        override fun setBlacklist(resultBean: ResultBean<*>?) {

        }

        override fun cleanBlacklist(resultBean: ResultBean<*>?) {

        }

        override fun setCalendar(resultBean: ResultBean<*>?) {

        }

        override fun resetLock(resultBean: ResultBean<*>?) {

        }

        override fun setLockFactory(resultBean: ResultBean<*>?) {

        }

        override fun resetLockFactory(resultBean: ResultBean<*>?) {

        }

        override fun setLockSerialId(resultBean: ResultBean<*>?) {

        }

        override fun readCardByCylinder(resultBean: ResultBean<*>?) {

        }

        override fun onReport(resultBean: ResultBean<*>?) {

        }

        override fun getReaderInfo(resultBean: ResultBean<*>?) {

        }

        override fun setReaderSerialId(resultBean: ResultBean<*>?) {

        }

        override fun getKeyboardInfo(resultBean: ResultBean<*>) {

        }

        override fun setKeyboardInfo(resultBean: ResultBean<*>?) {

        }

        override fun disPlay(resultBean: ResultBean<*>) {

        }

        override fun setTempCard(p0: ResultBean<*>?) {

        }

        override fun deleteTempCard(p0: ResultBean<*>?) {
        }

        override fun findTempCard(p0: ResultBean<*>?) {
        }

    }

    override fun onScanStarted() {


    }

    override fun onScanStopped() {
    }

    override fun onF9000Found(p0: IKeyScanInfo?) {


    }

    override fun onLockFound(p0: ILockScanInfo?, p1: IMobileCredentialScanInfo?) {
        requireActivity().runOnUiThread {
            if (p0!!.lockName == scanLockData.data.lock_uid) {
                scanManagerService.stopScan()
                lock = p0
                info = p1
                scanLockData.data.log(
                    action = LockLogActionType.AddLock,
                    message = "Lock Found",
                    data = JSON.toJSONString(
                        p0,
                        SerializerFeature.WriteDateUseDateFormat
                    ) + " && " + JSON.toJSONString(p1, SerializerFeature.WriteDateUseDateFormat)
                )
                openISEOLock()
            }

        }

    }

    private fun openISEOLock() {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                var response: ILockResponse? = null
                withContext(Dispatchers.IO) {
                    runCatching { Looper.prepare() }

                    locksManager
                        .scan(lockBrands = listOf(LockBrand.ISEO))
                        .first { it.name == scanLockData.data.unique_key }
                    runCatching {
                        locksManager.resetIseoTime(
                            id = scanLockData.data._id,
                            uniqueKey = scanLockData.data.unique_key,
                            locks = locksRepository
                        )
                    }.onFailure {
                        withContext(Dispatchers.Main) { requireActivity().appToast(it.message ?: "Error happened") }
                    }
                    iseoLock = ServiceProvider.mobileCredentialConnLockService.connect(lock)
                    response = ServiceProvider.mobileCredentialUserCmdService.openLock(iseoLock)
                    dialogLock.dismiss()
                    val battery = getBattery(response!!.batteryStatus)
                    mViewModel.postBatteryPercentage(
                        sharePrefs.token,
                        battery, scanLockData.data.internal_id
                    )
                    val bundle = Bundle()
                    bundle.putParcelable("scanLockModel", scanLockData.data)
                    bundle.putString("whichPlace", whichPlace)
                    bundle.putParcelable("list", argumentList)
                    CommonValues.loadFragment(
                        CreateLockFragment(), bundle, parentFragmentManager,
                        R.id.frameContainerAddLock
                    )
                    scanLockData.data.log(
                        action = LockLogActionType.AddLock,
                        message = "Lock Found",
                        data = response.toString()
                    )


                }
            } catch (e: V364SdkException) {
            }
        }

    }


    private fun getBattery(batteryStatus: ILockBatteryStatus): Int {
        var batteryCount = 1000

        when (batteryStatus.batteryStatus.name) {
            "BATTERY_OK" -> {
                batteryCount = 3
            }

            "BATTERY_LOW" -> {
                batteryCount = 2
            }

            "BATTERY_VERYLOW" -> {
                batteryCount = 1
            }

            "BATTERY_EXTRALOW" -> {
                batteryCount = 0
            }
        }

        return batteryCount
    }


    override fun onLegacyDeviceFound(p0: ILegacyScanInfo?) {

    }

    private suspend fun initLockWise() {
        airbnk.connect(decryptedAccessKey)
        airbnk.getByKey(decryptedAccessKey).setLockTime(Clock.System.now().toEpochMilliseconds())
        airbnk.getByKey(decryptedAccessKey).unlock()
        delay(5000)
        airbnk.getByKey(decryptedAccessKey).lock()
        foundLockWise()
        airbnk.disconnect()
    }

    private suspend fun foundLockWise() {
        dialogLock.dismiss()
        val bundle = Bundle()
        bundle.putParcelable("scanLockModel", scanLockData.data)
        bundle.putString("whichPlace", whichPlace)
        bundle.putParcelable("list", argumentList)
        CommonValues.loadFragment(
            CreateLockFragment(), bundle, parentFragmentManager,
            R.id.frameContainerAddLock
        )

        mViewModel.postBatteryPercentage(
            sharePrefs.token,
            airbnk.getByKey(decryptedAccessKey).batteryLevel(), scanLockData.data.internal_id
        )

        scanLockData.data.log(
            action = LockLogActionType.Unlock,
            message = "Unlock Successfully!",
            data = decryptedAccessKey
        )
    }

}