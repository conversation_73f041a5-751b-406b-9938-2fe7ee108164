package feature.pm.addlock

import android.Manifest
import android.content.ContentValues.TAG
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.google.gson.JsonObject
import com.google.zxing.Result
import com.karumi.dexter.Dexter
import com.karumi.dexter.PermissionToken
import com.karumi.dexter.listener.PermissionDeniedResponse
import com.karumi.dexter.listener.PermissionGrantedResponse
import com.karumi.dexter.listener.PermissionRequest
import com.karumi.dexter.listener.single.PermissionListener
import data.common.preferences.Constants
import data.common.preferences.Preferences
import data.network.android.GetAccessKeyModel
import data.network.android.models.ModelAdminInstaller
import data.utils.android.CommonValues
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.defaultDialog
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import feature.settings.maintenance.time.ResetTimeAndUnlockViewModel
import feature.settings.maintenance.time.UnlockActivity
import keyless.feature.pm.addlock.R
import keyless.feature.pm.addlock.databinding.FragmentScanBinding
import me.dm7.barcodescanner.zxing.ZXingScannerView

class ScanFragment : Fragment(), ZXingScannerView.ResultHandler {

    private var argumentList: ModelAdminInstaller.DataModelInstaller = ModelAdminInstaller.DataModelInstaller()
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            requireContext()
        )
    }
    lateinit var mViewModel: ScanFragmentViewModel
    private lateinit var binding: FragmentScanBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentScanBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initz()
        checkCameraPermission()
        observerInit()
    }

    private fun observerInit() {
        mViewModel.progress.observe(viewLifecycleOwner) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(requireContext(), true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        mViewModel.error.observe(viewLifecycleOwner) {
            requireContext().toast(it)
            binding.scanner.stopCamera()
            checkCameraPermission()
            binding.scanner?.setResultHandler(this)
        }
    }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[ScanFragmentViewModel::class.java]
        argumentList = arguments?.getParcelable("list")!!
    }

    private fun checkCameraPermission() {
        Dexter.withContext(context).withPermission(Manifest.permission.CAMERA)
            .withListener(object : PermissionListener, ZXingScannerView.ResultHandler {
                override fun onPermissionGranted(p0: PermissionGrantedResponse?) {
                    binding.scanner?.setResultHandler(this)
                    binding.scanner?.startCamera()
                }

                override fun onPermissionDenied(p0: PermissionDeniedResponse?) {
                    requireActivity().toast(getString(keyless.data.utils.android.R.string.must_need_camera_permission))
                    askPermission()
                }

                override fun onPermissionRationaleShouldBeShown(
                    p0: PermissionRequest?,
                    p1: PermissionToken?
                ) {
                    askPermission()
                }

                override fun handleResult(p0: Result?) {
                    p0?.let { Log.v(TAG, it.text) }
                    Log.v(TAG, p0?.barcodeFormat.toString())
                    binding.scanner?.resumeCameraPreview(this)
                }
            }).check()
    }

    private fun askPermission() {
        requestPermissions(
            arrayOf(
                Manifest.permission.CAMERA
            ),
            50
        )
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (
            grantResults.isNotEmpty() &&
            grantResults[0] == PackageManager.PERMISSION_GRANTED
        ) {
            binding.scanner?.setResultHandler(this)
            binding.scanner?.startCamera()
        } else {
            showDialogForPermissions()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10) {
            askPermission()
        } else if (
            requestCode == ResetTimeAndUnlockViewModel.RESET_TIME_UNLOCK_LOCK_REQUEST_CODE && resultCode == AppCompatActivity.RESULT_OK
        ) {
            assignLock()
        }
    }

    private fun showDialogForPermissions() {
        defaultDialog(
            requireActivity(),
            getString(keyless.data.utils.android.R.string.please_allow_permissions_to_continue),
            object : OnActionOK {
                override fun onClickData() {
                    if (activity != null && isAdded) {
                        startActivityForResult(
                            Intent(
                                Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                Uri.fromParts("package", requireActivity().packageName, null)
                            ),
                            10
                        )
                    }
                }
            }
        )
    }

    override fun onResume() {
        super.onResume()
        binding.scanner.resumeCameraPreview(this)
        binding.scanner?.setResultHandler(this)
    }

    override fun onPause() {
        super.onPause()
        binding.scanner.resumeCameraPreview(this)
        binding.scanner?.stopCamera()
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.scanner?.stopCamera()
    }

    override fun handleResult(p0: Result?) {
        binding.scanner.stopCamera()
        val res = p0.toString()
        if (res.isNotEmpty()) {
            val argumentPlace = arguments?.getString("whichPlace").toString()

            if (argumentPlace == "installer") {
                var included = false
                for (i in argumentList.locks) {
                    if (i.internal_id == res) {
                        included = true
                        break
                    }
                }

                if (included) {
                    apiImplimentation(argumentPlace, res)
                } else {
                    defaultDialog(
                        requireActivity(),
                        getString(keyless.data.utils.android.R.string.not_assigned_lock),
                        object : OnActionOK {
                            override fun onClickData() {
                                requireActivity().finish()
                            }
                        }
                    )
                }
            } else {
                apiImplimentation(argumentPlace, res)
            }
        } else {
            requireContext().toast(getString(keyless.data.utils.android.R.string.cannot_scan_this_code))
            binding.scanner.stopCamera()
            checkCameraPermission()
            binding.scanner?.setResultHandler(this)
        }
    }

    private fun apiImplimentation(argumentPlace: String, res: String) {
        mViewModel.scanLock(sharePrefs.token, res, argumentPlace, sharePrefs.uuid).observe(
            this
        ) {
            if (it.success) {
                gotAccessKeyModel(it)

//                val bundle = Bundle()
//                bundle.putParcelable("scanLockData", it)
//                bundle.putString("whichPlace", argumentPlace)
//                bundle.putParcelable("list", argumentList)
//                CommonValues.loadFragment(
//                    UnlockFragment(),
//                    bundle,
//                    parentFragmentManager,
//                    R.id.frameContainerAddLock
//                )
            } else {
                binding.scanner.stopCamera()
                checkCameraPermission()
            }
        }
    }

    private fun loadCreateLockFragment() {
        val bundle = Bundle()
        bundle.putParcelable("scanLockModel", mViewModel.dataKey)
        bundle.putString("whichPlace", arguments?.getString("whichPlace").toString())
        bundle.putParcelable("list", argumentList)

        CommonValues.loadFragment(
            CreateLockFragment(), bundle, parentFragmentManager,
            R.id.frameContainerAddLock
        )
    }

    private fun assignLock() {
        if (mViewModel.dataKey?.provider == Constants.iseo) {
            assignIseo()
        } else {
            loadCreateLockFragment()
        }
    }

    private fun gotAccessKeyModel(model: GetAccessKeyModel) {
        mViewModel.setDataAccess(model.data)
        startActivityForResult(
            /* intent = */ Intent(
                /* packageContext = */ context,
                /* cls = */ UnlockActivity::class.java
            ).putExtra(ResetTimeAndUnlockViewModel.RESET_TIME_UNLOCK_LOCK_UNIQUE_KEY, model.data.unique_key)
                .putExtra(ResetTimeAndUnlockViewModel.RESET_TIME_UNLOCK_LOCK_ID_KEY, model.data._id)
                .putExtra(ResetTimeAndUnlockViewModel.RESET_TIME_UNLOCK_LOCK_UID_KEY, model.data.lock_uid)
                .putExtra(ResetTimeAndUnlockViewModel.RESET_TIME_UNLOCK_LOCK_ACCESS_KEY, model.data.unlockAccessKey())
                .putExtra(ResetTimeAndUnlockViewModel.RESET_TIME_UNLOCK_LOCK_PROVIDER_KEY, model.data.provider)
                .putExtra(ResetTimeAndUnlockViewModel.RESET_TIME_UNLOCK_LOCK_INTERNAL_ID_KEY, model.data.internal_id),

            /* requestCode = */ ResetTimeAndUnlockViewModel.RESET_TIME_UNLOCK_LOCK_REQUEST_CODE
        )
    }

    private fun assignIseo() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("lock_id", mViewModel.dataKey?.unique_key)
        jsonObject.addProperty("uid", sharePrefs.uuid)
        mViewModel
            .assign_lock_iseo(sharePrefs.token, Preferences.userRole.get(), argumentList, jsonObject)
            .observe(requireActivity()) {
                loadCreateLockFragment()
            }
    }
}