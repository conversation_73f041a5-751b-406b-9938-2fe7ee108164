<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
        tools:context="feature.properties.AddProperty">

    <ImageView
        android:id="@+id/backBtnProperty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="20dp"
        android:layout_marginTop="16dp"
        app:chainUseRtl="true"
        style="@style/ImageMirror"
        app:guidelineUseRtl="true"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/propertyTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/add_building"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtnProperty"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtnProperty" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="0dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtnProperty"
        android:layout_height="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="30dp">

            <FrameLayout
                android:id="@+id/iconLayout"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginTop="25dp"
                android:background="@drawable/bg_button_rounded"
                android:backgroundTint="@color/white"
                android:elevation="5dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/constraintLayout2">

                <ImageView
                    android:id="@+id/placeHolder"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center"
                    android:src="@drawable/image_placeholder" />

            </FrameLayout>

            <TextView
                android:id="@+id/textView10"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/select_icon"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iconLayout" />

            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView15"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/select_location"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView10" />

            <EditText
                android:clickable="true"
                android:focusable="false"
                android:cursorVisible="false"
                android:id="@+id/selectLocET"
                android:layout_width="0dp"
                android:layout_marginTop="4dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:inputType="text|textCapWords"
                android:textColor="@color/black"
                android:drawableEnd="@drawable/marker2"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:hint="@string/select_location"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView15"
                app:layout_constraintTop_toBottomOf="@+id/textView15" />

<!--            <View-->
<!--                android:id="@+id/locationBtn"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="0dp"-->
<!--                app:layout_constraintBottom_toBottomOf="@+id/selectLocET"-->
<!--                app:layout_constraintEnd_toEndOf="@+id/selectLocET"-->
<!--                app:layout_constraintHorizontal_bias="0.0"-->
<!--                app:layout_constraintStart_toStartOf="@+id/selectLocET"-->
<!--                app:layout_constraintTop_toTopOf="@+id/selectLocET"-->
<!--                app:layout_constraintVertical_bias="0.0" />-->

            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/emirate"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/selectLocET" />

            <EditText
                android:id="@+id/emirateET"
                android:clickable="true"
                android:focusable="false"
                android:cursorVisible="false"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:inputType="text|textCapWords"
                android:layout_marginTop="4dp"
                android:textColor="@color/black"
                android:layout_marginEnd="20dp"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:hint="@string/emirate"
                android:drawableEnd="@drawable/direction_bottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView16"
                app:layout_constraintTop_toBottomOf="@+id/textView16" />

<!--            <View-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="0dp"-->
<!--                app:layout_constraintTop_toTopOf="@id/emirateET"-->
<!--                app:layout_constraintStart_toStartOf="@id/emirateET"-->
<!--                app:layout_constraintEnd_toEndOf="@id/emirateET"-->
<!--                app:layout_constraintBottom_toBottomOf="@id/emirateET"-->
<!--                android:id="@+id/emirateBtn"/>-->
            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView17"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/building_name"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/emirateET" />

            <EditText
                android:id="@+id/buildingET"
                android:layout_width="0dp"
                android:hint="@string/building_name"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_medium_500"
                android:layout_marginTop="4dp"
                android:textSize="16dp"
                android:inputType="text|textCapWords"
                android:textColor="@color/black"
                android:layout_marginEnd="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView17"
                app:layout_constraintTop_toBottomOf="@+id/textView17" />

            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView18"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/total_floor"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/buildingET" />

            <EditText
                android:id="@+id/floorET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:textColor="@color/black"
                android:layout_marginTop="4dp"
                android:inputType="number"
                android:hint="@string/total_floor"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView18"
                app:layout_constraintTop_toBottomOf="@+id/textView18" />

            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView19"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/maintenance_number"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/floorET" />

            <EditText
                android:paddingStart="8dp"
                android:id="@+id/maintenanceET"
                android:layout_width="0dp"
                android:hint="@string/_971xxxxxxxxxx"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:inputType="number"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:layout_marginTop="4dp"
                android:textSize="16dp"
                android:textColor="@color/black"
                android:maxLength="15"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView19"
                app:layout_constraintTop_toBottomOf="@+id/textView19" />
            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView20"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:textColor="@color/black"
                android:textSize="16dp"
                android:text="@string/laundry_number"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/maintenanceET" />

            <EditText
                android:paddingStart="8dp"
                android:id="@+id/laundryET"
                android:layout_width="0dp"
                android:hint="@string/_971xxxxxxxxxx"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:inputType="number"
                android:maxLength="15"
                android:includeFontPadding="false"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:layout_marginTop="4dp"
                android:textColor="@color/black"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView20"
                app:layout_constraintTop_toBottomOf="@+id/textView20" />


            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView22"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:textColor="@color/black"
                android:textSize="16dp"
                android:text="@string/support_call_number"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/laundryET" />

            <EditText
                android:id="@+id/supportCallET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:inputType="number"
                android:hint="@string/_971xxxxxxxxxx"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:layout_marginTop="4dp"
                android:textColor="@color/black"
                android:maxLength="15"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView22"
                app:layout_constraintTop_toBottomOf="@+id/textView22" />


            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView23"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:textColor="@color/black"
                android:textSize="16dp"
                android:text="@string/support_whatsapp_number"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/supportCallET" />

            <EditText
                android:id="@+id/supportWhatsappET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:hint="@string/_971xxxxxxxxxx"
                app:layout_constraintEnd_toEndOf="parent"
                android:maxLength="15"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:layout_marginTop="4dp"
                android:textColor="@color/black"
                android:inputType="number"
                app:layout_constraintStart_toStartOf="@+id/textView23"
                app:layout_constraintTop_toBottomOf="@+id/textView23" />

            <TextView
                style="@style/buttonStyle"
                android:id="@+id/saveBtn"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginStart="60dp"
                android:layout_marginTop="43dp"
                android:layout_marginEnd="60dp"
                android:layout_marginBottom="41dp"
                android:text="@string/btnSave"
                app:layout_constraintTop_toBottomOf="@+id/supportWhatsappET"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>