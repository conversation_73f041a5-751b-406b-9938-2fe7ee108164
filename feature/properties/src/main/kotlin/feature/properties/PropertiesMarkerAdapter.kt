package feature.properties

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import data.network.android.ApiUtils
import data.network.android.models.Property
import keyless.feature.properties.databinding.PropertiesMarkerAdapterLayoutBinding

class PropertiesMarkerAdapter() : RecyclerView.Adapter<PropertiesMarkerAdapter.ViewHolder>() {
    var context: Context? = null
    var listFiltered: ArrayList<Property> = ArrayList()
    private var listMainProperties: ArrayList<Property> = ArrayList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        context = parent.context
        val binding = PropertiesMarkerAdapterLayoutBinding.inflate(
            LayoutInflater.from(context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON>Hold<PERSON>, position: Int) {
        holder.bind(listFiltered[position], position)
    }

    inner class ViewHolder(val binding: PropertiesMarkerAdapterLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: Property, position: Int) {
            binding.markerItem.placeName.text = model.building_name
            binding.markerItem.addressMarker.text = model.area + ", " + model.emirate
            binding.markerItem.noFloorTxt.text = model.total_floors.toString()
            binding.markerItem.noLockTxt.text = model.total_locks.toString()
            if (model.icon?.isNotEmpty()!!) {
                model.icon_id?.let { icon ->
                    model.icon.let {
                        val iconModel = model.icon?.first { it._id == icon }
                        iconModel?.let {
                            binding.markerItem.iconProperty.background = (null)
                            Glide.with(context!!).load(ApiUtils.IMAGE_BASE_URL + it.icon)
                                .into(binding.markerItem.iconProperty)
                        }
                    }
                }
            }

            binding.mainLay.setOnClickListener {
                context?.startActivity(
                    Intent(context, feature.properties.PropertyDetailActivity::class.java).putExtra("model", model)
                )
            }
        }
    }

    override fun getItemCount() = listFiltered.size

    fun updateAdapter(list: ArrayList<Property>?) {
        listMainProperties = list!!
        listFiltered = listMainProperties
        notifyDataSetChanged()
    }
}