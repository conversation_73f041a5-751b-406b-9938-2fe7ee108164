<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context="feature.authentication.signup.AddYourMobileNumber">

    <TextView
        android:id="@+id/textView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/add_your_mobile_number"
        android:textColor="@color/black"
        android:fontFamily="@font/poppins_semibold_600"
        android:textSize="22dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView5"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/we_ll_need_to_confirmation"
        android:textColor="@color/black"
        android:textSize="16dp"
        android:fontFamily="@font/poppins_medium_500"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView" />
    <LinearLayout
        android:id="@+id/ll_country_code"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_edit_corners"
        android:layout_marginTop="34dp"
        android:orientation="horizontal"
        android:paddingStart="8dp"
        android:paddingTop="8dp"
        android:paddingEnd="12dp"
        android:paddingBottom="8dp"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView5">


        <com.mikhaellopez.circularimageview.CircularImageView
            android:id="@+id/iv_flag_img"
            app:civ_border="false"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:src="@drawable/flag_uae"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"/>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:src="@drawable/arrow_black_down" />

    </LinearLayout>


    <EditText
        android:id="@+id/tvMobileNumber"
        android:layout_width="0dp"
        android:layout_height="0dp"
        style="@style/mirrorText"
        android:layout_marginStart="20dp"
        android:background="@drawable/bg_edit_corners"
        android:fontFamily="@font/poppins_medium_500"
        android:hint="@string/enter_mobile_number"
        android:inputType="number"
        android:padding="8dp"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="@+id/ll_country_code"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/ll_country_code"
        app:layout_constraintTop_toTopOf="@+id/ll_country_code"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/txt_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:textColor="@color/black"
        android:textSize="14dp"
        android:fontFamily="@font/poppins_regular_400"
        android:text="@string/by_continuing_you_confirm_that_you"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_country_code" />

<!--    <TextView-->
<!--        android:id="@+id/signUpBtn"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="50dp"-->
<!--        android:layout_marginStart="60dp"-->
<!--        android:layout_marginEnd="60dp"-->
<!--        android:layout_marginBottom="54dp"-->
<!--        android:includeFontPadding="false"-->
<!--        android:background="@drawable/bg_btn_round"-->
<!--        android:fontFamily="@font/poppins_medium_500"-->
<!--        android:gravity="center"-->
<!--        android:text="@string/next"-->
<!--        android:textColor="@color/black"-->
<!--        android:textSize="16dp"-->
<!--        android:visibility="gone"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent" />-->

    <TextView
            android:id="@+id/signUpWhatsAppBtn"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="25dp"
            android:layout_marginEnd="25dp"
            android:layout_marginBottom="8dp"
            android:includeFontPadding="false"
            android:background="@drawable/bg_button_grey_rounded"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:text="@string/receive_by_whatsapp"
            android:textColor="@color/black"
            android:textSize="16dp"
            tools:visibility="visible"
            app:layout_constraintBottom_toTopOf="@+id/signUpSmsBtn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    <TextView
            android:id="@+id/signUpSmsBtn"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="25dp"
            android:layout_marginEnd="25dp"
            android:layout_marginBottom="8dp"
            android:includeFontPadding="false"
            android:background="@drawable/bg_button_grey_rounded"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:text="@string/receive_by_phone_sms"
            android:textColor="@color/black"
            android:textSize="16dp"
            tools:visibility="visible"
            app:layout_constraintBottom_toTopOf="@+id/signUpCallBtn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    <TextView
            android:id="@+id/signUpCallBtn"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="25dp"
            android:layout_marginEnd="25dp"
            android:layout_marginBottom="54dp"
            android:includeFontPadding="false"
            android:background="@drawable/bg_button_grey_rounded"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:text="@string/receive_by_phone_call"
            android:textColor="@color/black"
            android:textSize="16dp"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>