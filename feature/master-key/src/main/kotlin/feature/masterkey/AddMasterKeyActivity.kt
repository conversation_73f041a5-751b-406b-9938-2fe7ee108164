package feature.masterkey

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import data.utils.android.CommonValues
import keyless.feature.master.key.R

class AddMasterKeyActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_add_master_key)
        screenIntents()
    }

    private fun screenIntents() {
        if (CommonValues.isBluetoothEnabled()) {
            CommonValues.loadFragment(
                fragment = FindMasterKeyFragment(),
                supportFragmentManager = supportFragmentManager,
                layoutId = R.id.frameContainerMaster
            )
        } else {
            CommonValues.loadFragment(
                fragment = EnableBluetoothMasterFragment(),
                supportFragmentManager = supportFragmentManager,
                layoutId = R.id.frameContainerMaster
            )
        }
    }
}