<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/transparent"
    android:backgroundTint="@color/transparent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/onboardTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/access"
        android:gravity="center"
        android:textSize="34dp"
        android:fontFamily="@font/poppins_medium_500"
        android:textColor="@color/white"
        app:layout_constraintBottom_toTopOf="@+id/onboardSubTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:gravity="center"
        android:textSize="16dp"
        android:fontFamily="@font/poppins_regular_400"
        android:textColor="@color/white"
        android:id="@+id/onboardSubTitle"
        android:layout_width="0dp"
        app:layout_constraintWidth_percent="0.8"
        android:layout_height="wrap_content"
        android:text="@string/all_your_locks_from_one_app"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>