package feature.injection.domain

import data.settings.company.ViewModel
import data.settings.company.injection.CompanyProfileDomainScope
import org.koin.dsl.module

internal val companyProfileSettingsDomainModule = module {
    scope<CompanyProfileDomainScope> {
        scoped {
            ViewModel(
                logger = get(),
                company = get(),
                status = get()
            )
        }
    }
}