package feature.injection.data

import data.lock.common.lock.implementation.DefaultLocksRemote
import data.lock.common.lock.implementation.DefaultLocksRepository
import data.lock.common.lock.repositories.LocksRepository
import data.user.account.impl.DefaultUserAccountRepository
import data.user.account.repositories.UserAccountRepository
import data.user.guest.repositories.GuestRepository
import keyless.feature.injection.ConfigValues
import org.koin.dsl.module

internal val userAccountDataInjection = module {
    single<UserAccountRepository> {
        DefaultUserAccountRepository(
            client = get(),
            logger = get(),
            baseUrl = ConfigValues.baseUrl
        )
    }

    single<LocksRepository> {
        DefaultLocksRepository(
            remote = DefaultLocksRemote(
                client = get(),
                logger = get(),
                hostname = ConfigValues.baseUrl,
                lockInfoHostname = ConfigValues.baseUrl
            ),
            logger = get()
        )
    }
}

internal val userGuestDataInjection = module {
    single {
        GuestRepository(
            client = get(),
            baseUrl = ConfigValues.baseUrl,
            logger = get()
        )
    }
}