package feature.injection.data

import data.company.impl.DefaultCompanyRepository
import data.company.impl.DefaultCompanyStaffRepository
import data.company.repositories.CompanyRepository
import data.company.repositories.CompanyStaffRepository
import keyless.feature.injection.ConfigValues
import org.koin.dsl.module

val companyDataInjection = module {
    single<CompanyRepository> {
        DefaultCompanyRepository(
            client = get(),
            logger = get(),
            baseUrl = ConfigValues.baseUrl
        )
    }

    single<CompanyStaffRepository> {
        DefaultCompanyStaffRepository(
            client = get(),
            logger = get(),
            baseUrl = ConfigValues.baseUrl
        )
    }
}