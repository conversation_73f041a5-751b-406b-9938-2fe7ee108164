plugins {
    id("keyless.android.feature")
}

android {
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

dependencies {
    implementation(project(":data-common"))
    implementation(project(":data-error"))
    implementation(project(":data-company"))
    implementation(project(":domain-common"))
    implementation(project(":domain-settings-company-staff"))
    implementation(project(":feature-common"))

    implementation(keyless.androidx.lifecycle.livedata)
    implementation(keyless.circle.image.view)
    implementation("com.github.codersrouteandroid:flexible-switch:1.0")
}