<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/profileParent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    tools:context="feature.settings.company.staff.ManageStaffActivity">

    <ImageView
        android:id="@+id/backBtnManageStaff"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        style="@style/ImageMirror"
        android:padding="20dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/propertyTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/manage_staff"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtnManageStaff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtnManageStaff" />

    <ImageView
        android:id="@+id/addStaffBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:src="@drawable/staff_add_img"
        app:layout_constraintBottom_toBottomOf="@+id/propertyTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/propertyTitle" />

    <FrameLayout
        android:id="@+id/frameLayout2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtnManageStaff">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/manageStaffRV"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingTop="20dp" />

        <TextView
            android:id="@+id/noInternetLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawableTop="@drawable/no_internet"
            android:drawablePadding="15dp"
            android:fontFamily="@font/poppins_medium_500"
            android:src="@drawable/common_google_signin_btn_icon_dark_normal"
            android:text="@string/no_internet_connection"
            android:textColor="@color/black"
            android:textSize="16dp"
            android:visibility="gone" />


    </FrameLayout>


    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:drawableTop="@drawable/iv_no_staff_member_found"
        android:gravity="center"
        android:visibility="gone"
        android:textColor="@color/black"
        android:textSize="16dp"
        android:id="@+id/noStaffMember"
        android:fontFamily="@font/poppins_regular_400"
        android:text="@string/no_staff_member_found"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/frameLayout2" />



</androidx.constraintlayout.widget.ConstraintLayout>