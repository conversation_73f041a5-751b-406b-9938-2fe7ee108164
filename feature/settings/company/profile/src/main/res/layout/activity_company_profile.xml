<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    tools:context="feature.settings.company.CompanyProfileActivity">

    <ImageView
        android:id="@+id/backBtnCP"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="20dp"
        android:layout_marginTop="10dp"
        style="@style/ImageMirror"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/propertyTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/company_profile"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtnCP"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtnCP" />



    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtnCP">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="gone"
            android:id="@+id/noInternetLayout"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableTop="@drawable/no_internet"
                android:drawablePadding="15dp"
                android:fontFamily="@font/poppins_medium_500"
                android:src="@drawable/common_google_signin_btn_icon_dark_normal"
                android:text="@string/no_internet_connection"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.core.widget.NestedScrollView
            android:id="@+id/constraintMain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="0dp">


                <TextView
                    android:id="@+id/textView15"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="25dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:text="@string/company_name"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <EditText
                    android:id="@+id/cpNameET"
                    style="@style/mirrorText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="20dp"
                    android:hint="@string/company_name"
                    android:background="@drawable/bg_edit_corners"
                    android:fontFamily="@font/poppins_medium_500"
                    android:includeFontPadding="false"
                    android:inputType="text|textCapWords"
                    android:padding="14dp"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/textView15"
                    app:layout_constraintTop_toBottomOf="@+id/textView15" />

                <TextView
                    android:id="@+id/textView16"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:text="@string/address"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/cpNameET" />

                <EditText
                    android:id="@+id/cpAddress"
                    style="@style/mirrorText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:layout_marginTop="4dp"
                    android:hint="@string/address"
                    android:background="@drawable/bg_edit_corners"
                    android:fontFamily="@font/poppins_medium_500"
                    android:includeFontPadding="false"
                    android:inputType="text|textCapWords"
                    android:padding="14dp"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/textView16"
                    app:layout_constraintTop_toBottomOf="@+id/textView16" />


                <TextView
                    android:id="@+id/txtTimeZone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:text="@string/time_zone"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:visibility="visible"
                    app:layout_constraintStart_toStartOf="@+id/cpAddress"
                    app:layout_constraintTop_toBottomOf="@+id/cpAddress" />

                <EditText
                    android:id="@+id/etTimeZone"
                    style="@style/mirrorText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:layout_marginTop="4dp"
                    android:hint="@string/time_zone"
                    android:background="@drawable/bg_edit_corners"
                    android:clickable="true"
                    android:cursorVisible="false"
                    android:drawableEnd="@drawable/direction_bottom"
                    android:focusable="false"
                    android:fontFamily="@font/poppins_medium_500"
                    android:includeFontPadding="false"
                    android:inputType="text|textCapWords"
                    android:padding="14dp"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/txtTimeZone"
                    app:layout_constraintTop_toBottomOf="@+id/txtTimeZone" />


                <TextView
                    android:id="@+id/txtType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:text="@string/account_type"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:visibility="visible"
                    app:layout_constraintStart_toStartOf="@+id/cpAddress"
                    app:layout_constraintTop_toBottomOf="@+id/etTimeZone" />

                <EditText
                    android:id="@+id/etType"
                    style="@style/mirrorText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/bg_edit_corners"
                    android:clickable="true"
                    android:cursorVisible="false"
                    android:drawableEnd="@drawable/direction_bottom"
                    android:focusable="false"
                    android:fontFamily="@font/poppins_medium_500"
                    android:includeFontPadding="false"
                    android:inputType="text|textCapWords"
                    android:padding="14dp"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/txtType"
                    app:layout_constraintTop_toBottomOf="@+id/txtType" />



                <TextView
                    android:id="@+id/txtL"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:text="@string/trade_licence_authority_lia"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:visibility="visible"
                    app:layout_constraintStart_toStartOf="@+id/cpAddress"
                    app:layout_constraintTop_toBottomOf="@+id/etType" />

                <EditText
                    android:id="@+id/etL"
                    style="@style/mirrorText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/bg_edit_corners"
                    android:clickable="true"
                    android:cursorVisible="false"
                    android:drawableEnd="@drawable/direction_bottom"
                    android:focusable="false"
                    android:fontFamily="@font/poppins_medium_500"
                    android:includeFontPadding="false"
                    android:hint="@string/trade_licence_authority_lia"
                    android:inputType="text|textCapWords"
                    android:padding="14dp"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/txtL"
                    app:layout_constraintTop_toBottomOf="@+id/txtL" />


                <TextView
                    android:id="@+id/txtTradingNumber"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:text="@string/trading_licence_number"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:visibility="visible"
                    app:layout_constraintStart_toStartOf="@+id/textView16"
                    app:layout_constraintTop_toBottomOf="@+id/etL" />

                <EditText
                    android:id="@+id/etTradingNumber"
                    style="@style/mirrorText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/bg_edit_corners"
                    android:fontFamily="@font/poppins_medium_500"
                    android:includeFontPadding="false"
                    android:inputType="number"
                    android:layout_marginTop="4dp"
                    android:hint="@string/trading_licence_number"
                    android:maxLength="10"
                    android:padding="14dp"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/txtTradingNumber"
                    app:layout_constraintTop_toBottomOf="@+id/txtTradingNumber" />


                <TextView
                    android:id="@+id/textView17"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:text="@string/country"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/etTradingNumber" />

                <EditText
                    android:id="@+id/cpCountry"
                    style="@style/mirrorText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:hint="@string/country"
                    android:background="@drawable/bg_edit_corners"
                    android:fontFamily="@font/poppins_medium_500"
                    android:includeFontPadding="false"
                    android:inputType="text|textCapWords"
                    android:padding="14dp"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toStartOf="@id/cpCity"
                    app:layout_constraintStart_toStartOf="@+id/textView17"
                    app:layout_constraintTop_toBottomOf="@+id/textView17" />

                <TextView
                    android:id="@+id/textView19"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:text="@string/city"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="@+id/cpCity"
                    app:layout_constraintTop_toTopOf="@+id/textView17" />

                <EditText
                    android:id="@+id/cpCity"
                    style="@style/mirrorText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/bg_edit_corners"
                    android:clickable="true"
                    android:cursorVisible="false"
                    android:layout_marginTop="4dp"
                    android:drawableEnd="@drawable/direction_bottom"
                    android:focusable="false"
                    android:hint="@string/city"
                    android:fontFamily="@font/poppins_medium_500"
                    android:includeFontPadding="false"
                    android:inputType="text|textCapWords"
                    android:padding="14dp"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/cpCountry"
                    app:layout_constraintTop_toBottomOf="@+id/textView17" />

                <TextView
                    android:id="@+id/textView18"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:text="@string/zip_code"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/cpCountry" />

                <EditText
                    android:id="@+id/cpZipCode"
                    style="@style/mirrorText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="15dp"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/bg_edit_corners"
                    android:fontFamily="@font/poppins_medium_500"
                    android:includeFontPadding="false"
                    android:inputType="number"
                    android:padding="14dp"
                    android:maxLength="5"
                    android:hint="@string/zip_code"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/cpTrn"
                    app:layout_constraintStart_toStartOf="@+id/textView18"
                    app:layout_constraintTop_toBottomOf="@+id/textView18"
                    app:layout_constraintVertical_bias="0.0" />

                <TextView
                    android:id="@+id/textView20"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:text="@string/trn"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="@+id/cpTrn"
                    app:layout_constraintTop_toBottomOf="@+id/cpCountry" />

                <EditText
                    android:id="@+id/cpTrn"
                    style="@style/mirrorText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/bg_edit_corners"
                    android:fontFamily="@font/poppins_medium_500"
                    android:layout_marginTop="4dp"
                    android:includeFontPadding="false"
                    android:inputType="text|textCapWords"
                    android:padding="14dp"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:hint="@string/trn"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/cpZipCode"
                    app:layout_constraintTop_toBottomOf="@+id/textView18" />



                <TextView
                    android:layout_width="wrap_content"
                    android:text="@string/check_in_"
                    android:id="@+id/txtCheckInReq"
                    android:textColor="@color/black"
                    android:layout_marginTop="20dp"
                    android:visibility="visible"
                    android:textSize="16dp"
                    android:layout_marginStart="20dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/cpZipCode"
                    android:layout_height="wrap_content"/>




                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switchCheckIn"
                    style="@style/ImageMirror"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:checked="false"
                    android:textSize="15sp"
                    app:backgroundColorOnSwitchOff="@color/color_grey"
                    app:backgroundColorOnSwitchOn="@color/colorAccent"
                    app:layout_constraintBottom_toBottomOf="@+id/txtCheckInReq"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginEnd="20dp"
                    app:layout_constraintTop_toTopOf="@+id/txtCheckInReq"
                    app:showText="false"
                    app:strokeColorOnSwitchOff="@color/colorAccent"
                    app:strokeColorOnSwitchOn="@color/colorAccent"
                    app:textColorOnSwitchOff="@color/white"
                    app:textColorOnSwitchOn="@color/colorAccent"
                    app:thumbColorOnSwitchOff="#FFFFFF"
                    app:thumbColorOnSwitchOn="#FFFFFF" />


                <TextView
                    android:id="@+id/updateCP"
                    style="@style/buttonStyle"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginTop="50dp"
                    android:layout_marginBottom="30dp"
                    android:text="@string/update"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/txtCheckInReq"
                    app:layout_constraintWidth_percent="0.6" />


            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.core.widget.NestedScrollView>



    </androidx.constraintlayout.widget.ConstraintLayout>



</androidx.constraintlayout.widget.ConstraintLayout>