<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent">


    <ImageView
        android:id="@+id/backBtn"
        style="@style/ImageMirror"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:padding="20dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/titleToolbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/cards"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtn" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="15dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtn"
        app:layout_constraintVertical_bias="0.0">

        <EditText
            android:id="@+id/svCards"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="20dp"
            style="@style/mirrorText"
            android:background="@drawable/bg_btn_round"
            android:backgroundTint="@color/bg_edit_grey"
            android:drawableStart="@drawable/ic_baseline_search_24"
            android:drawablePadding="10dp"
            android:ellipsize="end"
            android:focusableInTouchMode="true"
            android:fontFamily="@font/poppins_regular_400"
            android:hint="@string/search_by_card_name"
            android:imeOptions="actionSearch"
            android:includeFontPadding="false"
            android:padding="8dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingRight="30dp"
            android:singleLine="true"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/stopSearchCards"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:src="@drawable/iv_cross_black"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/svCards"
            app:layout_constraintEnd_toEndOf="@+id/svCards"
            app:layout_constraintTop_toTopOf="@+id/svCards" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvCards"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/svCards"
            app:layout_constraintVertical_bias="0.0" />



        <LinearLayout
            android:id="@+id/layNoData"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/iv_no_managed_card" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/no_card_found"
                android:textColor="@color/black"
                android:textSize="14dp"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rv_lock" />



        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>


    <ProgressBar
        android:id="@+id/progress_pagination"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:theme="@style/progressBarBlue"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />



</androidx.constraintlayout.widget.ConstraintLayout>