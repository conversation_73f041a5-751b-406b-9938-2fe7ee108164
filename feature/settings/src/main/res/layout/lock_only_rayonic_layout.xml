<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/app"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="8dp"
    android:id="@+id/mainLay"
    android:theme="@style/Theme.MaterialComponents.Light"
    card_view:cardBackgroundColor="@color/card_bg_color"
    card_view:cardCornerRadius="10dp"
    card_view:cardElevation="0dp"
    card_view:cardMaxElevation="0dp"
    card_view:cardUseCompatPadding="true"
    card_view:strokeColor="@color/card_bg_color"
    card_view:strokeWidth="1dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/grey_round">


        <TextView
            android:id="@+id/txtLockName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:includeFontPadding="false"
            style="@style/mirrorText"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintHorizontal_bias="0.0"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/txtPropertyName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:textColor="@color/black"
            style="@style/mirrorText"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/txtLockName"
            app:layout_constraintTop_toBottomOf="@+id/txtLockName"
            app:layout_constraintVertical_bias="0.0"
            card_view:layout_constraintEnd_toEndOf="@+id/txtLockName"
            card_view:layout_constraintStart_toStartOf="@+id/txtLockName"
            card_view:layout_constraintTop_toBottomOf="@+id/txtLockName" />

        <TextView
            android:id="@+id/txtLockPlace"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:textColor="@color/black"
            style="@style/mirrorText"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/txtLockName"
            app:layout_constraintTop_toBottomOf="@+id/txtPropertyName"
            app:layout_constraintVertical_bias="0.0"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintEnd_toEndOf="@+id/txtPropertyName"
            card_view:layout_constraintHorizontal_bias="0.0"
            card_view:layout_constraintStart_toStartOf="@+id/txtLockName"
            card_view:layout_constraintTop_toBottomOf="@+id/txtPropertyName"
            card_view:layout_constraintVertical_bias="0.0" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>