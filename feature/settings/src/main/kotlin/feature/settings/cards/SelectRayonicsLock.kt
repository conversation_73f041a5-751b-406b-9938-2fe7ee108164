package feature.settings.cards

import android.Manifest
import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.app.Dialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.Intent
import android.content.IntentSender
import android.content.pm.PackageManager
import android.graphics.Rect
import android.location.LocationManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.Settings
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.KeyEvent
import android.view.Window
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import android.window.OnBackInvokedCallback
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.serializer.SerializerFeature
import com.google.android.gms.common.api.GoogleApiClient
import com.google.android.gms.common.api.PendingResult
import com.google.android.gms.common.api.Status
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsResult
import com.google.android.gms.location.LocationSettingsStatusCodes
import com.google.gson.JsonObject
import com.iseo.v364sdk.services.exception.V364SdkException
import com.iseo.v364sdk.services.mobilecredentialservice.IMobileCredentialService
import com.iseo.v364sdk.services.mobilecredentialservice.IMobileHermesCmdService
import com.iseo.v364sdk.services.mobilecredentialservice.model.ILock
import com.iseo.v364sdk.services.mobilecredentialservice.model.user.ILockResponse
import com.iseo.v364sdk.services.scanservice.IScanManagerService
import com.iseo.v364sdk.services.scanservice.model.IKeyScanInfo
import com.iseo.v364sdk.services.scanservice.model.ILegacyScanInfo
import com.iseo.v364sdk.services.scanservice.model.ILockScanInfo
import com.iseo.v364sdk.services.scanservice.model.IMobileCredentialScanInfo
import com.iseo.v364sdk.services.scanservice.model.IScanBTManagerEvent
import com.ttlock.bl.sdk.api.ExtendedBluetoothDevice
import com.ttlock.bl.sdk.api.TTLockClient
import com.ttlock.bl.sdk.callback.ScanLockCallback
import com.ttlock.bl.sdk.entity.LockError
import com.wdullaer.materialdatetimepicker.time.TimePickerDialog
import core.lock.airbnk.AirBnkManager
import core.lock.airbnk.AndroidAirBnkManager
import core.locks.logs.models.LockLogActionType
import data.common.preferences.Preferences
import data.network.android.DataModelCard
import data.network.android.LocksListResponse
import data.network.android.UserSharedModel
import data.network.android.log
import data.utils.android.CommonValues
import data.utils.android.applications.ServiceProvider
import data.utils.android.dialogs.ChatEditText
import data.utils.android.hideKeyboard
import data.utils.android.isNotValidEmail
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import domain.common.ErrorHandler
import dots.animation.textview.TextAndAnimationView
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.appToast
import feature.common.dialogs.defaultDialog
import feature.common.models.ModelForData
import feature.common.navigation.logoutAndNavToDashboard
import feature.settings.changelock.ChangeLockActivity
import feature.settings.profile.ProfileViewModel
import keyless.data.utils.android.R
import keyless.feature.settings.databinding.ActivitySelectRayonicsLockBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import rayo.logicsdk.bean.BleLockScanData
import rayo.logicsdk.bean.LockCodeClass
import rayo.logicsdk.bean.PermissionActionEnum
import rayo.logicsdk.bean.PermissionTypeEnum
import rayo.logicsdk.ble.BleScanCallback
import rayo.logicsdk.ble.BluetoothLeScan
import rayo.logicsdk.data.LockBasicInfo
import rayo.logicsdk.data.LockPermissionData
import rayo.logicsdk.data.LockPermissionData.PermissionData
import rayo.logicsdk.sdk.BleLockSdk
import rayo.logicsdk.sdk.BleLockSdkCallback
import rayo.logicsdk.sdk.ResultBean
import java.text.DateFormat
import java.text.DateFormatSymbols
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone

class SelectRayonicsLock :
    AppCompatActivity(),
    AdapterSelectRayonicsLock.ClickToConnect,
    IScanBTManagerEvent {

    private var timeForCalender: String = ""
    private var decryptedAccessKey: String = ""
    private var searchUser: Boolean = false
    private lateinit var dataMain: DataModelCard
    private var screen: Int = 1
    private var selectedItem: LocksListResponse.LocksModel = LocksListResponse.LocksModel()
    private lateinit var adapterLock: AdapterSelectRayonicsLock
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    private var mBleLockSdk: BleLockSdk? = null
    private var mBluetoothManager: BluetoothManager? = null
    private var mBleName: MutableList<String> = ArrayList()
    private var mBluetoothLeScan: BluetoothLeScan? = null
    private var mBleHandler: BleHandler? = null
    private var isScan = false
    private var mBluetoothDeviceHashMap: HashMap<String, BleLockScanData>? = null
    private var mBluetoothDevice: BleLockScanData? = null
    private var mMac: String? = null
    private var mLockBasicInfo: LockBasicInfo? = null
    private lateinit var validEndDateApi: Date
    private lateinit var validStartDateApi: Date
    private val mViewModel: ProfileViewModel by viewModel()
    lateinit var mViewModelSearch: SelectRayonicsViewModel
    private var endDateApi: String = ""
    private var startDateApi: String = ""
    private var userOnlyDetail = UserSharedModel()
    var userNameApi = ""
    var emailForApi = ""
    var phoneApi = ""
    private lateinit var scanManagerService: IScanManagerService
    private lateinit var mobileCredentialService: IMobileCredentialService
    private lateinit var mobileCredentialHermesCmdService: IMobileHermesCmdService
    private var lock: ILockScanInfo? = null
    private var info: IMobileCredentialScanInfo? = null
    private var iseoLock: ILock? = null
    private var backed: Boolean = false
    private var REQUEST_CHECK_SETTINGS = 3
    lateinit var dialogLock: Dialog
    private lateinit var binding: ActivitySelectRayonicsLockBinding

    val startActivityIntent = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult(),
        ActivityResultCallback() {
            if (it.resultCode == 50) {
                dataMain = it.data?.getParcelableExtra<DataModelCard>("modelCards")!!
                binding.etCardId.setText(dataMain.internal_id)
            }
        })


    private val airbnk: AirBnkManager by inject()
    private val handler: ErrorHandler by inject()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        (airbnk as? AndroidAirBnkManager)?.bind(this)
        binding = ActivitySelectRayonicsLockBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initz()
        apiChecking()
        setAdapter()
        askPermission()
        clickListeners()
        observerInit()
    }

    private fun askPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_CONNECT, Manifest.permission.BLUETOOTH_SCAN,
                ), 50
            )
        } else {
            requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION,
                ), 50
            )
        }
    }

    private fun initIseoLock() {
        scanManagerService = ServiceProvider.scanManagerService
        mobileCredentialService = ServiceProvider.mobileCredentialService
        mobileCredentialHermesCmdService =
            ServiceProvider.mobileCredentialHermesCmdService
        scanManagerService.setScanBTManagerEvent(this)
        scanISEOLock()
    }

    private fun scanISEOLock() {
        CoroutineScope(Dispatchers.Main).launch {
            withContext(Dispatchers.IO) {
                scanManagerService.stopScan()
                try {
                    scanManagerService.startScanLock(false)
                } catch (e: V364SdkException) {

                }
            }
        }

    }


    private fun apiChecking() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("uid", sharePrefs.uuid)
        jsonObject.addProperty("is_admin", Preferences.isAdminLogin())
        mViewModel.getCheckUser(sharePrefs.token, jsonObject).observe(this) {
            Preferences.timeZoneName.set(it.timezone_name)
            Preferences.timeZoneOffset.set(it.timezone)
            if (!it.success) {
                logoutAndNavToDashboard(it.message, this)
            }

        }

    }


    @SuppressLint("MissingPermission")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (
                    grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED && grantResults[1] == PackageManager.PERMISSION_GRANTED) {
                    if (!CommonValues.isBluetoothEnabled()) {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    } else {
                        initializationCommon()
                    }
                } else {
                    showDialogForPermissions()
                }
            } else if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {

                val lm = getSystemService(LOCATION_SERVICE) as LocationManager
                var gps_enabled = false
                var network_enabled = false

                try {
                    gps_enabled = lm.isProviderEnabled(LocationManager.GPS_PROVIDER)
                } catch (ex: java.lang.Exception) {
                }

                try {
                    network_enabled = lm.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
                } catch (ex: java.lang.Exception) {
                }

                if (!gps_enabled && !network_enabled) {
                    displayLocationSettingsRequest()
                } else {
                    if (CommonValues.isBluetoothEnabled()) {
                        initializationCommon()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                }
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun displayLocationSettingsRequest() {

        val googleApiClient = GoogleApiClient.Builder(this)
            .addApi(LocationServices.API).build()
        googleApiClient.connect()
        val locationRequest: LocationRequest = LocationRequest.create()
        locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        locationRequest.interval = 10000
        locationRequest.fastestInterval = 10000 / 2
        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
        builder.setAlwaysShow(true)
        val result: PendingResult<LocationSettingsResult> =
            LocationServices.SettingsApi.checkLocationSettings(googleApiClient, builder.build())
        result.setResultCallback { result ->
            val status: Status = result.status
            when (status.statusCode) {
                LocationSettingsStatusCodes.SUCCESS -> {
                    if (CommonValues.isBluetoothEnabled()) {
                        initializationCommon()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                }

                LocationSettingsStatusCodes.RESOLUTION_REQUIRED -> {
                    try {
                        status.startResolutionForResult(
                            this@SelectRayonicsLock,
                            REQUEST_CHECK_SETTINGS
                        )
                    } catch (e: IntentSender.SendIntentException) {
                    }
                }

                LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE -> {}
            }
        }
    }

    private fun initializationCommon() {
        if (selectedItem.lock.provider != "") {
            if (selectedItem.lock.provider == CommonValues.keyless) {
                initLock()
            } else if (selectedItem.lock.provider == CommonValues.iseo) {
                initIseoLock()
            } else if (
                selectedItem.lock.provider == CommonValues.oji || selectedItem.lock.provider == CommonValues.linko
            ) {
                initOjiLock()
            } else if (selectedItem.lock.provider == CommonValues.lockWise) {
                initAirBnk()
            }
        }

    }

    private fun initOjiLock() {
        binding.progressBar.isVisible = true
        TTLockClient.getDefault().startScanLock(object : ScanLockCallback {
            override fun onScanLockSuccess(device: ExtendedBluetoothDevice?) {
                if (selectedItem.lock.unique_key.contains(device!!.name) || device.name.contains(selectedItem.lock.unique_key)) {
                    findLockDone()
                    TTLockClient.getDefault().stopScanLock()
                    selectedItem.lock.log(
                        action = LockLogActionType.ScanLock,
                        message = "Scan Lock Successfully!",
                        data = decryptedAccessKey
                    )
                }
            }

            override fun onFail(error: LockError?) {
                selectedItem.lock.log(
                    action = LockLogActionType.ScanError,
                    message = error.toString(),
                    data = decryptedAccessKey
                )
            }
        })


    }

    private fun findLockDone() {

        val modelData = ModelForData()
        modelData.unique_key = selectedItem.lock.unique_key
        modelData.access_key = selectedItem.lock.access_key
        modelData.lock_id = selectedItem.lock._id
        modelData.owner_id = selectedItem.owner_id
        modelData.provider = selectedItem.lock.provider
        modelData.encrypted_key = selectedItem.lock.encrypted_key
        modelData.internal_id = selectedItem.lock.internal_id
        startActivity(
            Intent(
                this@SelectRayonicsLock,
                ChangeLockActivity::class.java
            ).putExtra("selectedItem", modelData)
        )
        dialogLock.dismiss()
        binding.progressBar.isVisible = false

    }

    private fun showDialogForPermissions() {
        defaultDialog(
            this,
            getString(R.string.please_allow_bluetooth),
            object : OnActionOK {
                override fun onClickData() {
                    startActivityForResult(
                        Intent(
                            Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                            Uri.fromParts("package", packageName, null),
                        ), 10
                    )
                }
            })
    }

    private fun observerInit() {
        mViewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        mViewModel.error.observe(this) {
            toast(it)
        }

    }

    private fun clickListeners() {

        if (Build.VERSION.SDK_INT > 32) {
            onBackInvokedDispatcher.registerOnBackInvokedCallback(
                0,
                (object : OnBackPressedCallback(true),
                    OnBackInvokedCallback {
                    override fun handleOnBackPressed() {

                    }

                    override fun onBackInvoked() {
                        if (screen == 1) {
                            mBleLockSdk?.disconnect()
                            finish()
                        } else if (screen == 2) {
                            binding.lockListView.isVisible = true
                            binding.selectLockView.isVisible = false
                            binding.btnAddCard.isVisible = false
                            if (intent.hasExtra("maintenance")) {
                                binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.select_lock)
                            } else {
                                binding.titleToolbar.text = getString(
                                    keyless.data.utils.android.R.string.select_rayonics_lock
                                )
                            }

                            screen = 1
                            binding.linearResults.isVisible = true
                            binding.tvUserName.isVisible = false
                            binding.etUserName.isVisible = false
                            binding.tvMobileNumber.isVisible = false
                            binding.etMobileNumber.isVisible = false
                            binding.userDetails.isVisible = false
                            binding.tvStartTime.isVisible = false
                            binding.etStartTime.isVisible = false
                            binding.tvEndTime.isVisible = false
                            binding.etEndTime.isVisible = false
                            binding.etStartTime.setText("")
                            binding.etEndTime.setText("")
                            binding.txtName.text = ""
                            binding.txtEmail.text = ""
                            binding.txtPhone.text = ""
                            binding.etEmail.setText("")
                            binding.etCardId.setText("")

                            mBleLockSdk?.disconnect()

                        }
//        running = false
//        start.cancel()
                        backed = true
                    }
                })
            )
        }


        binding.etEmail.setKeyImeChangeListener(object : ChatEditText.KeyImeChange {
            override fun onKeyIme(keyCode: Int, event: KeyEvent?) {
                if (KeyEvent.KEYCODE_BACK == event?.keyCode) {
                    performSearch(0)
                }
            }
        })

        binding.etEmail.setOnEditorActionListener(TextView.OnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                performSearch(1)
                return@OnEditorActionListener true
            }
            false
        })


        binding.searchEmailBtn.setOnClickListener {
            performSearch(1)
        }


        binding.btnAddCard.setOnClickListener {

            if (!binding.linearResults.isVisible) {
                performSearch(1)
            } else {
                if (validations()) {
                    val lockPermissionData: LockPermissionData = object : LockPermissionData() {
                        override fun findPermission(permissionData: PermissionData) {}
                        override fun setPermission(
                            count: Int,
                            pos: Int,
                            permissionData: PermissionData
                        ) {
                        }
                    }
                    lockPermissionData.permissionData.add(
                        PermissionData(
                            PermissionActionEnum.ADD_ENUM,
                            dataMain.card_uid,
                            PermissionTypeEnum.CARD_ID_ENUM,
                            validStartDateApi,
                            validEndDateApi, true, true, true, true, true, true, true, true, true, true
                        )
                    )
                    mViewModel.setProgress(true)
                    mBleLockSdk?.updateLockPermission(lockPermissionData, false)
                }
            }

        }

        binding.etStartTime.setOnClickListener {
            selectDate(1)
        }

        binding.etEndTime.setOnClickListener {
            if (binding.etStartTime.text.toString().trim().isEmpty()) {
                toast(getString(keyless.data.utils.android.R.string.please_select_start_time_first))
            } else {
                selectDate(2)
            }
        }

        binding.etCardId.setOnClickListener {
            if (Build.VERSION.SDK_INT == Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                startActivityIntent.launch(Intent(this, CardsActivity::class.java))
            } else {
                startActivityForResult(Intent(this, CardsActivity::class.java), 40)
            }
        }



        binding.backBtn.setOnClickListener {
            if (screen == 1) {
                finish()
            } else if (screen == 2) {
                binding.lockListView.isVisible = true
                binding.selectLockView.isVisible = false
                binding.btnAddCard.isVisible = false
                if (intent.hasExtra("maintenance")) {
                    binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.select_lock)
                } else {
                    binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.select_rayonics_lock)
                }
                binding.linearResults.isVisible = true
                binding.tvUserName.isVisible = false
                binding.etUserName.isVisible = false
                binding.tvMobileNumber.isVisible = false
                binding.etMobileNumber.isVisible = false
                binding.userDetails.isVisible = false
                binding.tvStartTime.isVisible = false
                binding.etStartTime.isVisible = false
                binding.tvEndTime.isVisible = false
                binding.etEndTime.isVisible = false
                binding.etStartTime.setText("")
                binding.etEndTime.setText("")
                binding.txtName.text = ""
                binding.txtEmail.text = ""
                binding.txtPhone.text = ""
                binding.etEmail.setText("")
                binding.etCardId.setText("")
                screen = 1
//                start?.cancelTimer()
                mBleLockSdk?.disconnect()
            }
            backed = true
//            running = false
//            start.cancel()
        }

        binding.svLock.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {

            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                adapterLock.let {
                    adapterLock.filter.filter(p0.toString())
                    binding.stopSearch.isVisible = p0.toString().isNotEmpty()
                }
            }

            override fun afterTextChanged(p0: Editable?) {

            }

        })

        binding.stopSearch.setOnClickListener {
            hideKeyboard()
            binding.svLock.setText("")
            adapterLock.filter.filter("")
        }
    }

    private fun validations(): Boolean {
        if (binding.etCardId.text.isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_select_card_id_to_continue))
        } else if (binding.etEmail.text!!.isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_email_to_continue))
        } else if (binding.etStartTime.text.isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_select_start_time_to_continue))
        } else if (binding.etEndTime.text.isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_select_end_time_to_continue))
        }

        if (binding.etStartTime.text.toString().isNotEmpty() && binding.etEndTime.text.toString().isNotEmpty()) {
            val dateStart = CommonValues.formateCompare(
                binding.etStartTime.text.toString()
            )

            val dateEnd = CommonValues.formateCompare(
                binding.etEndTime.text.toString().replace("\"", "")
            )

            try {
                val formatter = SimpleDateFormat("dd-MM-yyyy HH:mm", Locale("en"))
                val str1 = dateStart
                val date1 = formatter.parse(str1)
                val str2 = dateEnd
                val date2 = formatter.parse(str2)
                if (date1 > date2) {
                    toast(
                        getString(keyless.data.utils.android.R.string.end_date_time_should_be_greater_than_start_date)
                    )
                    return false
                } else if (date1 == date2) {
                    toast(getString(keyless.data.utils.android.R.string.start_time_end_time_should_not_be_the_same))
                    return false
                } else {
                    return true
                }
            } catch (e1: ParseException) {
                e1.printStackTrace()
            }
        } else {
            return false
        }


        return true
    }

    private fun performSearch(i: Int) {

        if (i != 0) {
            hideKeyboard()
        }

        if (binding.etEmail.text.toString().isNotValidEmail()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_valid_email))
            binding.linearResults.isVisible = false
            binding.tvUserName.isVisible = false
            binding.etUserName.isVisible = false
            binding.tvMobileNumber.isVisible = false
            binding.etMobileNumber.isVisible = false
            binding.userDetails.isVisible = false
            binding.tvStartTime.isVisible = false
            binding.etStartTime.isVisible = false
            binding.tvEndTime.isVisible = false
            binding.etEndTime.isVisible = false

        } else {
            mViewModelSearch.hitSearchUsersApi(
                sharePrefs.token,
                selectedItem.lock.unique_key,
                binding.etEmail.text.toString()
            ).observe(
                this
            ) {
                if (it.success) {
                    userOnlyDetail = it.users[0]
                    binding.linearResults.isVisible = true
                    binding.tvUserName.isVisible = false
                    binding.etUserName.isVisible = false
                    binding.tvMobileNumber.isVisible = false
                    binding.etMobileNumber.isVisible = false
                    binding.userDetails.isVisible = true
                    binding.txtName.text =
                        userOnlyDetail.detail.first_name + " " + userOnlyDetail.detail.last_name
                    binding.txtEmail.text = userOnlyDetail.detail.email
                    binding.txtPhone.text =
                        userOnlyDetail.detail.country_code + " " + userOnlyDetail.detail.mobile_number
                    userNameApi =
                        userOnlyDetail.detail.first_name + " " + userOnlyDetail.detail.last_name
                    emailForApi =
                        userOnlyDetail.detail.email
                    phoneApi = userOnlyDetail.detail.mobile_number


                    searchUser = true

                } else {
                    binding.linearResults.isVisible = true
                    binding.tvUserName.isVisible = true
                    binding.etUserName.isVisible = true
                    binding.tvMobileNumber.isVisible = true
                    binding.etMobileNumber.isVisible = true
                    binding.userDetails.isVisible = false
                    searchUser = false

                }

                binding.tvStartTime.isVisible = true
                binding.etStartTime.isVisible = true
                binding.tvEndTime.isVisible = true
                binding.etEndTime.isVisible = true

            }

        }


    }


    private fun selectDate(value: Int) {
        val currentDateTime = Calendar.getInstance()
        currentDateTime.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        val startYear = currentDateTime.get(Calendar.YEAR)
        val startMonth = currentDateTime.get(Calendar.MONTH)
        val startDay = currentDateTime.get(Calendar.DAY_OF_MONTH)
        val startHour = currentDateTime.get(Calendar.HOUR_OF_DAY)
        val startMinute = currentDateTime.get(Calendar.MINUTE)


        val dpd = DatePickerDialog(
            this,
            { _, year, month, day ->

                val tpd: TimePickerDialog = TimePickerDialog.newInstance(
                    { view, hourOfDay, minute, second ->
                        val pickedDateTime = Calendar.getInstance()
                        pickedDateTime.timeZone =
                            TimeZone.getTimeZone(Preferences.timeZoneName.get())

                        pickedDateTime.set(year, month, day, hourOfDay, minute)
                        var mMonth = (month + 1).toString()
                        var mDay = (day).toString()
                        var mHour = (hourOfDay).toString()
                        var mMinute = (minute).toString()

                        if (mMonth.length == 1) {
                            mMonth = "0$mMonth"
                        }
                        if (mDay.length == 1) {
                            mDay = "0$mDay"
                        }
                        if (mHour.length == 1) {
                            mHour = "0$mHour"
                        }
                        if (mMinute.length == 1) {
                            mMinute = "0$mMinute"
                        }
//                        val month = DateFormatSymbols().shortMonths[mMonth.toInt() - 1]
                        if (value == 1) {
                            val month = DateFormatSymbols.getInstance(Locale("en")).shortMonths[mMonth.toInt() - 1]
                            binding.etStartTime.setText("$mDay-$month-${year} ${mHour}:${mMinute}")
                            var dateForApi = "$year-$mMonth-${mDay} ${mHour}:${mMinute}"
                            val formatter: DateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale("en"))
                            val date = formatter.parse(dateForApi) as Date
                            println("Today is " + date.time)
                            validStartDateApi = date
                            val date2 = pickedDateTime.time
                            timeForCalender = convertToUtc2(date2)
                            startDateApi = convertToUtc(date2)
                            Log.d("// startDateApi" + convertToUtc(date2), "")
                        } else {
                            val month = DateFormatSymbols.getInstance(Locale("en")).shortMonths[mMonth.toInt() - 1]
                            binding.etEndTime.setText("$mDay-$month-${year} ${mHour}:${mMinute}")
                            val dateForApi = "$year-$mMonth-${mDay} ${mHour}:${mMinute}"
                            val formatter: DateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale("en"))
                            val date = formatter.parse(dateForApi) as Date
                            println("Today is " + date.time)
                            validEndDateApi = date
                            val date2 = pickedDateTime.time
                            endDateApi = convertToUtc(date2)
                            Log.d("// endDateApi" + convertToUtc(date2), "")
                        }
                    },
                    startHour,
                    startMinute,
                    false
                )

                var mon = month + 1
                var m = mon.toString()
                m = if (month.toString().length == 1) {
                    "0$mon"
                } else {
                    mon.toString()
                }
                var d = ""
                d = if (day.toString().length == 1) {
                    "0$day"
                } else {
                    day.toString()
                }

                var isDateSame = false

                if (value == 2) {
                    val hourStart = CommonValues.dateForHourWithoutTimezone(
                        timeForCalender
                    )
                    val minuteStart = CommonValues.dateForMinWithoutTimeZone(
                        timeForCalender
                    )

                    val dateStart = CommonValues.formatOnlyDateWithoutTimeZone(
                        timeForCalender
                    )
                    val dateEnd = "$d/$m/$year"

                    try {
                        val formatter = SimpleDateFormat("dd/MM/yyyy", Locale("en"))
                        val str1 = dateStart
                        val date1 = formatter.parse(str1)
                        val str2 = dateEnd
                        val date2 = formatter.parse(str2)
                        isDateSame = date1 == date2
                    } catch (e1: ParseException) {
                        e1.printStackTrace()
                    }


                    if (isDateSame) {
                        tpd.setMinTime(hourStart.toInt(), minuteStart.toInt(), 0)
                    }

                } else {
                    val dateStart = startDay.toString() + "/" + (startMonth + 1) + "/" + startYear
                    val dateEnd = "$d/$m/$year"

                    try {
                        val formatter = SimpleDateFormat("dd/MM/yyyy", Locale("en"))
                        val str1 = dateStart
                        val date1 = formatter.parse(str1)
                        val str2 = dateEnd
                        val date2 = formatter.parse(str2)
                        isDateSame = date1 == date2
                    } catch (e1: ParseException) {
                        e1.printStackTrace()
                    }

                    if (isDateSame) {
                        tpd.setMinTime(startHour, startMinute, 0)

                    }
                }

                tpd.show(supportFragmentManager, "TimePickerDialog")

            },
            startYear,
            startMonth,
            startDay
        )

        if (value == 1) {
            dpd.datePicker.minDate = currentDateTime.timeInMillis
        } else {
            val date = CommonValues.dateFor(startDateApi)
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale("en"))
            val maxDateCalendar = Calendar.getInstance()
            maxDateCalendar.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
            maxDateCalendar.time = dateFormat.parse(date)!!
            dpd.datePicker.minDate = maxDateCalendar.timeInMillis
        }

        dpd.show()


    }

    private fun convertToUtc(date2: Date): String {
        val calendar = Calendar.getInstance()
        calendar.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        calendar.time = date2
//        val time = calendar.time
        val finalCalender = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
        finalCalender.timeZone = TimeZone.getTimeZone("UTC")
        val format2 = finalCalender.format(calendar.time)
        return "$format2+0000"

    }


    private fun convertToUtc2(date2: Date): String {
        val calendar = Calendar.getInstance()
//        calendar.timeZone = TimeZone.getTimeZone("UTC")
        calendar.time = date2
        val time = calendar.time
        val finalCalender = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
//        finalCalender.timeZone = TimeZone.getTimeZone("UTC")
        val format2 = finalCalender.format(time)
        return "$format2+0000"

    }


    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        if (screen == 1) {
            mBleLockSdk?.disconnect()
            finish()
        } else if (screen == 2) {
            binding.lockListView.isVisible = true
            binding.selectLockView.isVisible = false
            binding.btnAddCard.isVisible = false
            if (intent.hasExtra("maintenance")) {
                binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.select_lock)
            } else {
                binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.select_rayonics_lock)
            }

            screen = 1
            binding.linearResults.isVisible = true
            binding.tvUserName.isVisible = false
            binding.etUserName.isVisible = false
            binding.tvMobileNumber.isVisible = false
            binding.etMobileNumber.isVisible = false
            binding.userDetails.isVisible = false
            binding.tvStartTime.isVisible = false
            binding.etStartTime.isVisible = false
            binding.tvEndTime.isVisible = false
            binding.etEndTime.isVisible = false
            binding.etStartTime.setText("")
            binding.etEndTime.setText("")
            binding.txtName.text = ""
            binding.txtEmail.text = ""
            binding.txtPhone.text = ""
            binding.etEmail.setText("")
            binding.etCardId.setText("")

            mBleLockSdk?.disconnect()

        }
//        running = false
//        start.cancel()
        backed = true

    }

    private fun setAdapter() {
        val mainList = ArrayList<LocksListResponse.LocksModel>()
        if (intent.hasExtra("maintenance")) {
            for (i in sharePrefs.getLockData()) {
                if (
                    i.lock.provider == CommonValues.keyless ||
                    i.lock.provider == CommonValues.iseo ||
                    i.lock.provider == CommonValues.oji ||
                    i.lock.provider == CommonValues.linko ||
                    i.lock.provider == CommonValues.lockWise
                ) {
                    mainList.add(i)
                }
            }
        } else {
            for (i in sharePrefs.getLockData()) {
                if (i.lock.provider == "Keyless") {
                    mainList.add(i)
                }
            }
        }

        if (mainList.size > 0) {
            binding.layNoDataFullPage.isVisible = false
            binding.rvRayonicLocks.isVisible = true
            binding.rvRayonicLocks.layoutManager = LinearLayoutManager(this)
            adapterLock = AdapterSelectRayonicsLock(mainList, this)
            binding.rvRayonicLocks.adapter = adapterLock
        } else {
            binding.layNoDataFullPage.isVisible = true
            binding.rvRayonicLocks.isVisible = false
            binding.lockListView.isVisible = false
        }

    }


    @SuppressLint("HandlerLeak")
    private inner class BleHandler : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            try {
                when (msg.what) {
                    CommonValues.ADD_ADAPTER -> {
                        if (msg.obj != null) {
                            try {
                                addAdapterItemRange(msg.obj as BleLockScanData)
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        }
//                        if (!running) {
//                            running = true
//                            start = object : CountDownTimerExt(20000, 10) {
//                                override fun onTimerTick(millisUntilFinished: Long) {
//                                    if (msg.obj != null) {
//                                        try {
//                                            addAdapterItemRange(msg.obj as BleLockScanData)
//                                        } catch (e: Exception) {
////                                            running = false
//                                            e.printStackTrace()
//                                        }
//                                    }
//                                }
//
//                                override fun onTimerFinish() {
////                                    if (!backed) {
//                                        runOnUiThread {
//                                            if (!isFinishing) {
//                                                try {
//                                                    progressBar.isVisible = false
//                                                    defaultDialog(
//                                                        this@SelectRayonicsLock,
//                                                        getString(R.string.scanned_lock_could_not_found),
//                                                        object : OnActionOK {
//                                                            override fun onClickData() {
//                                                                mBluetoothLeScan!!.stopReceiver()
//                                                                mBleLockSdk?.disconnect()
////                                                                running = false
//                                                            }
//                                                        })
//                                                } catch (e: BadTokenException) {
//                                                    Log.e("WindowManagerBad ", e.toString())
//                                                }
//                                            }
//                                        }
//
//                                    }
////                                }
//
//                            }
//                            start?.start()
//                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }


    private fun addAdapterItemRange(bluetoothDevice: BleLockScanData) {
        if (checkName(bluetoothDevice.bleName)) {
            if (!mBluetoothDeviceHashMap!!.containsKey(bluetoothDevice.bleMac)) {
                addItemIn(bluetoothDevice)
                mBluetoothDeviceHashMap!![bluetoothDevice.bleMac] = bluetoothDevice
            }
        }
    }

    private fun checkName(bleName: String): Boolean {
        for (ble in bleName) {
            if (bleName.contains(ble)) {
                return true
            }
        }
        return false
    }

    private fun addItemIn(bluetoothDevice: BleLockScanData) {
        if (bluetoothDevice == null) {
            return
        }
        mBluetoothDevice = bluetoothDevice
        if (mBluetoothDevice!!.bleName.contains(selectedItem.lock.unique_key)) {
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
//            start?.cancelTimer()
            connectLock()
        } else {
            isScan = true
        }
    }

    private fun connectLock() {
        mMac = mBluetoothDevice?.bleMac
        mBleLockSdk = BleLockSdk()
        mLockBasicInfo = LockBasicInfo()
        mBluetoothManager = getSystemService(BLUETOOTH_SERVICE) as BluetoothManager?
        mBleLockSdk?.init(mBluetoothManager, mBleLockSdkCallback)
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({
            var lockCodeClass = LockCodeClass()
            lockCodeClass.ivCode = "8qli5ljbkBJSBip8".toByteArray()
            lockCodeClass.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
            lockCodeClass.sysCode = decryptedAccessKey.toByteArray()
            mBleLockSdk?.connect(
                lockCodeClass,
                mBluetoothManager,
                this,
                mBluetoothDevice?.bleMac,
                mBluetoothDevice?.scanRecord,
                "1".toByteArray(),
                Date(),
                false
            )
        }, 10)

    }


    private val mBleLockSdkCallback: BleLockSdkCallback = object : BleLockSdkCallback {
        override fun init(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                init sdk  
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
            rayo.logicsdk.utils.Log.d(
                "TAG",
                JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )
        }

        override fun connect(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
            connect 
            ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
            """.trimIndent(),
                this@SelectRayonicsLock
            )
            selectedItem.lock.log(
                action = LockLogActionType.Unlock,
                message = "Connect Successfully!",
                data = resultBean.toString()
            )
        }

        override fun authentication(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """ authentication ${
                    JSON.toJSONString(
                        resultBean,
                        SerializerFeature.WriteDateUseDateFormat
                    )
                }""", this@SelectRayonicsLock
            )

            if (resultBean.isRet) {
//                if (!backed) {
                if (intent.hasExtra("maintenance")) {
                    binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.select_lock)
                    mBleLockSdk?.disconnect()
                    isScan = false
                    val modelData = ModelForData()
                    modelData.unique_key = selectedItem.lock.unique_key
                    modelData.access_key = selectedItem.lock.access_key
                    modelData.lock_id = selectedItem.lock._id
                    modelData.owner_id = selectedItem.owner_id
                    modelData.provider = selectedItem.lock.provider
                    modelData.encrypted_key = selectedItem.lock.encrypted_key
                    modelData.internal_id = selectedItem.lock.internal_id
                    startActivity(
                        Intent(
                            this@SelectRayonicsLock,
                            ChangeLockActivity::class.java
                        ).putExtra("selectedItem", modelData)
                    )
                    binding.progressBar.isVisible = false
                } else {
                    binding.progressBar.isVisible = false
                    screen = 2
                    binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.configure_card)
                    binding.etSelectedLock.setText(selectedItem.lock.name)
                    binding.selectLockView.isVisible = true
                    binding.btnAddCard.isVisible = true
                    binding.lockListView.isVisible = false
//                    start?.cancelTimer()
                }
//                mBleLockSdk?.disconnect()
                dialogLock.dismiss()
//                } else {
//                    mBleLockSdk?.disconnect()
//                }

            } else {
                mViewModel.setProgress(false)
//                progressBar.isVisible = false
//                if (JSON.toJSONString(
//                        resultBean,
//                        SerializerFeature.WriteDateUseDateFormat
//                    ).toLowerCase().contains("timeout")
//                ) {
//                    CommonValues.saveLockLogs(
//                        "",
//                        selectedItem.lock.internal_id,
//                        "Authentication timeout",
//                        "Unlock",
//                        resultBean.toString(),
//                        this@SelectRayonicsLock
//                    )
////                    runOnUiThread {
////                        if (!isFinishing) {
////                            defaultDialog(
////                                this@SelectRayonicsLock,
////                                getString(R.string.the_lock_could_not_be_connected),
////                                object : OnActionOK {
////                                    override fun onClickData() {
////                                    }
////                                })
////                        }
////                    }
//                } else {
//                    toast(
//                        JSON.toJSONString(
//                            resultBean,
//                            SerializerFeature.WriteDateUseDateFormat
//                        )
//                    )
//                }
            }

        }

        fun endTimeFunction(): Date {
            val sdf = SimpleDateFormat("MM/dd/yyyy HH:mm:ss")
            val currentDateandTime =
                Calendar.MONTH.toString() + "/" + Calendar.DAY_OF_MONTH + "/" + Calendar.YEAR + " 00:00:00"
            val date = sdf.parse(currentDateandTime)
            val calendar = Calendar.getInstance()
            calendar.time = date
            calendar.add(Calendar.MINUTE, 10)
            return calendar.time
        }

        override fun disConnect(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                disConnect 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
//            running = false
        }

        override fun registerLock(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                registerLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun getLockInfo(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                getLockInfo 
                ${resultBean.obj}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun setLockInfo(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockInfo 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun setLockTime(resultBean: ResultBean<*>?) {
//            CommonValues.showMessage(
//                """
//                setLockTime
//                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
//                """.trimIndent(),
//                this@SelectRayonicsLock
//            )
        }

        override fun getLockOfficeMode(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                getLockOfficeMode 
                ${resultBean.obj}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun setLockOfficeMode(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                setLockOfficeMode 
                ${resultBean.obj}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun getLockHotPoint(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                getLockHotPoint 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun setLockHotPoint(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockHotPoint 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun checkLockHotPoint(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                checkLockHotPoint 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun getLockStatus(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                getLockStatus 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun readLockEvent(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                readLockEvent 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun cleanLockEvent(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                cleanLockEvent 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun updateLockPermission(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                updateLockPermission 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )

            if (resultBean!!.isRet) {
                mViewModel.setProgress(true)
                val jsonObject = JsonObject()
                jsonObject.addProperty("card_id", dataMain.internal_id)
                jsonObject.addProperty("start_time", startDateApi)
                jsonObject.addProperty("end_time", endDateApi)
                jsonObject.addProperty("lock_id", selectedItem.lock._id)
                if (searchUser) {
                    jsonObject.addProperty("user_name", userNameApi)
                    jsonObject.addProperty("email", emailForApi)
                    jsonObject.addProperty("mobile_number", phoneApi)
                } else {
                    jsonObject.addProperty("user_name", binding.etUserName.text.toString().trim())
                    jsonObject.addProperty("email", binding.etEmail.text.toString().trim())
                    jsonObject.addProperty("mobile_number", binding.etMobileNumber.text.toString().trim())
                }

                mViewModel.configureCard(sharePrefs.token, jsonObject)
                    .observe(this@SelectRayonicsLock) {
                        if (it.success) {
                            mBleLockSdk?.disconnect()
                            setResult(151)
                            finish()
                        } else {
                            toast(it.message)
                        }
                    }
            } else {
                mViewModel.setProgress(false)
            }
        }

        override fun findLockPermission(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                findLockPermission 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun cleanLockPermission(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                cleanLockPermission 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun bleOpenLock(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                bleOpenLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )

        }

        override fun pincodeOpenLock(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                pincodeOpenLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun findLockBlacklist(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                findLockBlacklist 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun setBlacklist(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setBlacklist 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun cleanBlacklist(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                cleanBlacklist 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun setCalendar(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setCalendar 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun resetLock(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                resetLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun setLockFactory(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockFactory 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun resetLockFactory(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                resetLockFactory 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun setLockSerialId(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockSerialId 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun readCardByCylinder(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                readCardByCylinder 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )

        }

        override fun onReport(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                onReport 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun getReaderInfo(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                get reader info 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun setReaderSerialId(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                set reader serialId 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun getKeyboardInfo(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                get keyboard info 
                ${resultBean.obj}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun setKeyboardInfo(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                set keyboard info 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(), this@SelectRayonicsLock
            )
        }

        override fun disPlay(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                Bluetooth Data 
                ${resultBean.obj as String}
                """.trimIndent(),
                this@SelectRayonicsLock
            )
        }

        override fun setTempCard(p0: ResultBean<*>?) {


        }

        override fun deleteTempCard(p0: ResultBean<*>?) {

        }

        override fun findTempCard(p0: ResultBean<*>?) {

        }

    }


    private fun initz() {
        mViewModelSearch = ViewModelProvider(this)[SelectRayonicsViewModel::class.java]
        binding.selectLockView.isVisible = false

    }

    override fun onResume() {
        super.onResume()
//        running = false
    }

    override fun clickConnecting(locksModel: LocksListResponse.LocksModel) {
        selectedItem = locksModel

        if (CommonValues.isBluetoothEnabled()) {
            if (selectedItem.lock.encrypted_key.isNotEmpty()) {
                val decryptedKey =
                    CommonValues.decrypt(selectedItem.lock.encrypted_key, sharePrefs.uuid)
                if (decryptedKey == null) {
                    decryptedAccessKey = ""
                } else if (selectedItem.lock.encrypted_key.isNotEmpty()) {
                    decryptedAccessKey = decryptedKey
                }
            }
            showDialogForScanning()
//            progressBar.isVisible = true
            askPermission()
        } else {

        }


    }

    private fun showDialogForScanning() {
        dialogLock = Dialog(this)
        dialogLock.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialogLock.window?.setBackgroundDrawableResource(keyless.feature.common.R.drawable.white_all_corners_10)
        dialogLock.setCancelable(false)
        dialogLock.setContentView(keyless.feature.common.R.layout.lock_scanning_dialog)
        val animatedDots = dialogLock.findViewById<TextAndAnimationView>(
            keyless.feature.common.R.id.animatedDotsDialog
        )
        val cancelBtn = dialogLock.findViewById<TextView>(keyless.feature.common.R.id.cancelBtn)
        animatedDots.animate()
        dialogLock.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialogLock.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
            val maxWidth = displayRectangle.width() * 0.8f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight)

        }
        cancelBtn.setOnClickListener {
            mBleLockSdk?.disconnect()
            dialogLock.dismiss()
        }
        dialogLock.show()

    }

    private fun initLock() {
        mBleName = ArrayList<String>()
        mBleName.add(CommonValues.EXTRA_C_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_D_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_B_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_E_BLE_NAME)
        mBluetoothLeScan =
            object : BluetoothLeScan(BluetoothAdapter.getDefaultAdapter(), 0, mBleScanCallback) {
                override fun enableBluetooth(): Boolean {
                    return true
                }
            }
        mBleHandler = BleHandler()
        isScan = false
        mBluetoothDeviceHashMap = HashMap()
        Handler().postDelayed({
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
            mBluetoothDeviceHashMap = HashMap()
            mBluetoothLeScan!!.startReceiver()
            isScan = true
        }, 1000)

    }


    private val mBleScanCallback: BleScanCallback = object : BleScanCallback {
        @SuppressLint("MissingPermission")
        override fun findBle(bluetoothDevice: BluetoothDevice, rssi: Int, scanRecord: ByteArray) {
            val bleLockScanData = BleLockScanData(
                bluetoothDevice.name ?: "",
                bluetoothDevice.address ?: "",
                scanRecord
            )
            mBleHandler!!.obtainMessage(CommonValues.ADD_ADAPTER, bleLockScanData).sendToTarget()
        }

        override fun finishScan() {}
    }

    @SuppressLint("MissingPermission")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == 50) {
            dataMain = data?.getParcelableExtra<DataModelCard>("modelCards")!!
            binding.etCardId.setText(dataMain.internal_id)
        }

        if (requestCode == 10) {
            askPermission()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode != 0) {
            initializationCommon()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode == 0) {
            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
        } else if (requestCode == REQUEST_CHECK_SETTINGS) {
            displayLocationSettingsRequest()
        }
    }

    override fun onScanStarted() {


    }

    override fun onScanStopped() {

    }

    override fun onF9000Found(p0: IKeyScanInfo?) {

    }

    override fun onLockFound(p0: ILockScanInfo?, p1: IMobileCredentialScanInfo?) {
        runOnUiThread {
            if (p0!!.lockName == selectedItem.lock.lock_uid) {
                scanManagerService.stopScan()
                lock = p0
                info = p1
                openISEOLock()
            }
        }
    }

    private fun openISEOLock() {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                var response: ILockResponse? = null

                withContext(Dispatchers.IO) {
                    iseoLock = ServiceProvider.mobileCredentialConnLockService.connect(lock)
                    if (iseoLock!!.isConnected) {
                        val modelData = ModelForData()
                        modelData.unique_key = selectedItem.lock.unique_key
                        modelData.access_key = selectedItem.lock.access_key
                        modelData.lock_id = selectedItem.lock._id
                        modelData.lock_uid = selectedItem.lock.lock_uid
                        modelData.provider = selectedItem.lock.provider
                        modelData.owner_id = selectedItem.owner_id
                        modelData.encrypted_key = selectedItem.lock.encrypted_key
                        modelData.internal_id = selectedItem.lock.internal_id
                        dialogLock.dismiss()
                        startActivity(
                            Intent(
                                this@SelectRayonicsLock,
                                ChangeLockActivity::class.java
                            ).putExtra("selectedItem", modelData)
                        )
                        CoroutineScope(Dispatchers.Main).launch {
                            binding.progressBar.isVisible = false
                        }

                    }
                }
            } catch (e: V364SdkException) {
                e.printStackTrace()
            }
        }

        scanManagerService.stopScan()
        disconnectIseoLock()


    }

    private fun disconnectIseoLock() {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                withContext(Dispatchers.IO) {

                mobileCredentialHermesCmdService.disconnect(iseoLock)
                }


            } catch (e: V364SdkException) {
            }
        }

    }

    override fun onLegacyDeviceFound(p0: ILegacyScanInfo?) {

    }

    private fun initAirBnk() {
        binding.progressBar.isVisible = true

        lifecycleScope.launch {
            handler.async(
                onError = {
                    binding.progressBar.isVisible = false
                    appToast(it.message ?: "Error handler")
                }
            ) {
                airbnk.connect(decryptedAccessKey)
                binding.progressBar.isVisible = false
                selectedItem.lock.log(
                    action = LockLogActionType.ScanLock,
                    message = "Scan Lock Successfully!",
                    data = decryptedAccessKey
                )
                findLockDone()
            }
        }
    }

}