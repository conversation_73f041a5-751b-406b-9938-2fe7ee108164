package feature.settings.admin

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import data.network.android.models.UserModelAdmin
import keyless.feature.settings.databinding.AdapterUserListBinding

class AdapterUsersAdmin(context: Context) :
    RecyclerView.Adapter<AdapterUsersAdmin.ViewHolder>() {

    var listFiltered = ArrayList<UserModelAdmin>()
    var listener = context as ClickToLogin
    lateinit var context: Context

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = AdapterUserListBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(listFiltered[position])
    }

    override fun getItemCount() = listFiltered.size

    inner class ViewHolder(val binding: AdapterUserListBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: UserModelAdmin) {
            binding.txtName.text = model.first_name + " " + model.last_name
            binding.txtEmail.text = model.email
            binding.txtPhoneNumber.text = model.country_code + " " + model.mobile_number
            binding.txtRole.text = model.role
            binding.txtRole.text = model.role.capitalize()

            binding.mainLay.setOnClickListener {
                listener.clickLogin(model)
            }
        }
    }

    fun updateList(user: ArrayList<UserModelAdmin>) {
        val listFiltered1: ArrayList<UserModelAdmin> = user
        listFiltered = listFiltered1
        notifyDataSetChanged()
    }

    interface ClickToLogin {
        fun clickLogin(model: UserModelAdmin)
    }
}