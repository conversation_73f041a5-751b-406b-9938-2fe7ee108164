package feature.settings.profile

import domain.settings.profile.models.SettingsProfileDomainScope
import feature.settings.profile.update.ProfileViewModel
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.core.component.getScopeId
import org.koin.dsl.module

val profileFeatureInjection = module {
    viewModel {
        val scope = getKoin().getOrCreateScope<SettingsProfileDomainScope>(SettingsProfileDomainScope.getScopeId())
        ProfileViewModel(
            viewModel = scope.get(),
            handler = scope.get(),
            onClear = { scope.close() }
        )
    }
}