plugins {
    id("keyless.android.feature")
}

android {
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":core-permissions-manager"))
    implementation(project(":data-common"))
    implementation(project(":data-error"))
    implementation(project(":data-user-guest"))
    implementation(project(":domain-common"))
    implementation(project(":domain-settings-checkin"))
    implementation(project(":feature-common"))
    implementation(project(":feature-regula-refactored"))

}