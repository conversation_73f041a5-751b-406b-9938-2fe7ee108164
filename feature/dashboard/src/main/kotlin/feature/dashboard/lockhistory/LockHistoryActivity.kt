package feature.dashboard.lockhistory

import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.View.GONE
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.JsonObject
import data.common.preferences.Preferences
import data.network.android.models.AccessLogs
import feature.common.dialogs.ProgressDialogUtils
import data.utils.android.interfaces.PaginationScrollListener
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import keyless.feature.dashboard.R
import keyless.feature.dashboard.databinding.ActivityLockHistoryBinding

class LockHistoryActivity : AppCompatActivity() {
    private var id: String= ""
    private var whichSide: Int = 0
    private var listTotalAccess: ArrayList<AccessLogs> = ArrayList()
    private var listTotalShared: ArrayList<AccessLogs> = ArrayList()
    private var isLastPageAccess: Boolean = false
    private var isLastPageShared: Boolean = false
    private val layoutManagerAccess = LinearLayoutManager(this)
    private val layoutManagerShared = LinearLayoutManager(this)
    private lateinit var adapterAccess: AccessAdapter
    private lateinit var adapterShared: SharedAdapter
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    lateinit var mViewModel: LockHistoryViewModel
    var pageAccess = 1
    var pageShared = 1
    var isLoadingMoreAccess = false
    var isLoadingMoreShared = false
    private lateinit var binding: ActivityLockHistoryBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLockHistoryBinding.inflate(layoutInflater)
        setContentView(binding.root)
        apiInitialization(0, pageAccess, "no")
        setClickEvents()
        setUpAccessRecyclerView()
        setUpSharedRecyclerView()
        accessSetUp()
        observerInit()
        scrollListeners()
    }

    private fun scrollListeners() {
        if (whichSide == 0) {
            binding.rvAccess.addOnScrollListener(object :
                PaginationScrollListener(layoutManagerAccess) {
                override fun isLastPage(): Boolean {
                    return isLastPageAccess
                }

                override fun loadMoreItems() {
                    if (!isLoadingMoreAccess) {
                        pageAccess++
                        apiInitialization(0, pageAccess, "yes")
                        binding.progressPagination.visibility = View.VISIBLE
                    }
                }

                override fun isLoading(): Boolean {
                    return isLoadingMoreAccess
                }

            })
        } else if (whichSide == 1) {
            binding.rvShared.addOnScrollListener(object :
                PaginationScrollListener(layoutManagerShared) {
                override fun isLastPage(): Boolean {
                    return isLastPageShared
                }

                override fun loadMoreItems() {
                    if (!isLoadingMoreShared) {
                        isLoadingMoreShared = true
                        pageShared++
                        apiInitialization(1, pageShared, "yes")
                        binding.progressPagination.visibility = View.VISIBLE
                    }
                }

                override fun isLoading(): Boolean {
                    return isLoadingMoreShared
                }

            })
        }
    }

    private fun observerInit() {

        mViewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        mViewModel.error.observe(this) {
            if (it == getString(keyless.feature.common.R.string.you_are_in_offline)) {
                if (whichSide == 0) {

                    //check main and backup
                    if (sharePrefs.getLockHistoryMain()
                            .isEmpty() && sharePrefs.getLockHistoryBackup().isEmpty()
                    ) {
                        binding.noInternetLayout.visibility = View.VISIBLE
                        adapterAccess.updateList(arrayListOf())
                        adapterShared.updateList(arrayListOf())
                        return@observe
                    }

                    //check both
                    binding.noInternetLayout.visibility = View.GONE
                    binding.noDataView?.visibility = View.GONE
                    binding.rvAccess?.visibility = View.VISIBLE
                    binding.rvShared?.visibility = View.GONE
                    binding.progressPagination.visibility = View.GONE

                    val list = ArrayList<AccessLogs>()

                    //fetch backup
                    val backup = sharePrefs.getLockHistoryBackup()
                    backup.reverse()
                    if (backup.isNotEmpty()) {
                        for (i in backup) {
                            if (id == i.lockId) {
                                val model = AccessLogs()
                                model.user_name = Preferences.userFullName.get()
                                model.profile_photo = ""
                                model.user_id = ""
                                model.status = "1"
                                model.assignmentStatus = 1
                                model.date = ""
                                model.time = ""
                                model.device_model = i.device_model
                                model.mobile_id = i.mobile_id
                                model.created_at = i.created_at
                                model.valid_from = ""
                                model.valid_to = ""
                                list.add(model)
                            }
                        }
                    }


                    //fetch main
                    val main = sharePrefs.getLockHistoryMain()
                    if (main.isNotEmpty()) {
                        list.addAll(main)
                    }


                    adapterAccess.updateList(list)
                    isLastPageAccess = true
                    pageAccess = 1

                } else {
                    binding.noInternetLayout.visibility = View.VISIBLE
//                    adapterAccess.updateList(arrayListOf())
                    adapterShared.updateList(arrayListOf())
                }
            } else {
                toast(it)
            }
        }

        mViewModel.getResponseHistory.observe(this) {
//            if (it.success == true) {

            if (it.whichScreen == 0) {
                if (it.accessLogs?.isNotEmpty() == true) {
                    binding.noDataView.visibility = View.GONE
                    binding.rvAccess.visibility = View.VISIBLE
                    binding.rvShared.visibility = View.GONE
                    if (pageAccess == 1) {
                        isLastPageAccess = false
                        //saving data
                        sharePrefs.setLockHistoryMain(it.accessLogs!!)
                        listTotalAccess.clear()
                    }
                    listTotalAccess.addAll(it.accessLogs!!)
                    adapterAccess.updateList(listTotalAccess)
                    Log.e("total_count", it.total_count.toString())
                    if (it.total_count == listTotalAccess.size) {
                        isLastPageAccess = true
                        isLoadingMoreAccess = true
                    }
                    binding.progressPagination.visibility = View.GONE
                } else {
                    if (listTotalAccess.isEmpty()) {
                        binding.noDataView.visibility = View.VISIBLE
                        binding.noDataText.text = getString(keyless.data.utils.android.R.string.no_access_history_found)
                        binding.rvAccess.visibility = View.GONE
                        binding.rvShared.visibility = View.GONE
                    } else {
                        isLastPageAccess = true
                    }
                }
            } else {
                if (it.accessLogs != null && (it.accessLogs?.size ?: 0) > 0) {
                    binding.noDataView.visibility = View.GONE
                    binding.rvAccess.visibility = View.GONE
                    binding.rvShared.visibility = View.VISIBLE
                    if (pageShared == 1) {
                        isLastPageShared = false
                        listTotalShared.clear()
                    }
                    listTotalShared.addAll(it.accessLogs!!)
                    adapterShared.updateList(listTotalShared)
                    if (it.total_count == listTotalShared.size) {
                        isLastPageShared = true
                        isLoadingMoreShared = true
                    }
                    binding.progressPagination.visibility = View.GONE
                } else {
                    if (listTotalShared.isEmpty()) {
                        binding.noDataView.visibility = View.VISIBLE
                        binding.rvAccess.visibility = View.GONE
                        binding.noDataText.text = getString(keyless.data.utils.android.R.string.no_shared_history_found)
                        binding.rvShared.visibility = View.GONE
                    } else {
                        isLastPageShared = true
                        isLoadingMoreShared = true
                    }
                }
            }
            binding.noInternetLayout?.visibility = GONE
//            } else {
//                if (it.whichScreen == 0) {
//                    noDataView.visibility = View.VISIBLE
//                    noDataText.text = "No Access History Found"
//                    rvAccess.visibility = View.GONE
//                    rvShared.visibility = View.GONE
//
//                } else {
//                    noDataView.visibility = View.VISIBLE
//                    rvAccess.visibility = View.GONE
//                    noDataText.text = "No Shared History Found"
//                    rvShared.visibility = View.GONE
//                }
//            }
        }
    }

    private fun apiInitialization(whichApi: Int, page: Int, paginationStatus: String) {
        mViewModel = ViewModelProvider(this)[LockHistoryViewModel::class.java]
        whichSide = whichApi
        id = intent.getStringExtra("id").toString()
        val jsonObject = JsonObject()
        jsonObject.addProperty("page", page.toString())
        if (whichApi == 0) {
            //check if any backup
            if (page == 1) {
                val historyBackup = sharePrefs.getLockHistoryBackup()
                if (historyBackup.isNotEmpty()) {
                    mViewModel.updateLogs(
                        this,
                        sharePrefs.token,
                        historyBackup, true
                    ) {
                        mViewModel.hitLocksAccessApi(
                            sharePrefs.token,
                            id ?: "", jsonObject, paginationStatus
                        )
                    }
                } else {
                    mViewModel.hitLocksAccessApi(
                        sharePrefs.token,
                        id ?: "", jsonObject, paginationStatus
                    )
                }
            } else {
                mViewModel.hitLocksAccessApi(
                    sharePrefs.token,
                    id ?: "", jsonObject, paginationStatus
                )
            }
        } else {
            mViewModel.hitLocksSharedApi(
                sharePrefs.token,
                id ?: "", jsonObject, paginationStatus
            )
        }
    }

    private fun setClickEvents() {
        binding.tvAccess.setOnClickListener {
            pageAccess = 1
            apiInitialization(0, 1, "no")
            accessSetUp()
            scrollListeners()
        }

        binding.tvShared.setOnClickListener {
            var pageShared = 1
            apiInitialization(1, 1, "no")
            sharedSetUp()
            scrollListeners()
        }

        binding.viewBack.setOnClickListener {
            finish()
        }
    }

    private fun sharedSetUp() {
        binding.rvAccess.visibility = View.GONE
        binding.rvShared.visibility = View.VISIBLE
        binding.tvShared.background = ContextCompat.getDrawable(this, R.drawable.services_layout_bg)
        binding.tvAccess.setBackgroundResource(0)
        binding.tvAccess.setTextColor(ContextCompat.getColor(this, keyless.feature.common.R.color.black))
        binding.tvShared.setTextColor(ContextCompat.getColor(this, keyless.feature.common.R.color.white))
        val typeface = ResourcesCompat.getFont(this, keyless.data.utils.android.R.font.poppins_medium_500)
        binding.tvShared.typeface = typeface
        val typeface1 = ResourcesCompat.getFont(this, keyless.data.utils.android.R.font.poppins_regular_400)
        binding.tvAccess.typeface = typeface1
    }

    private fun accessSetUp() {

        binding.tvAccess.background = ContextCompat.getDrawable(this, R.drawable.services_layout_bg)
        binding.tvShared.setBackgroundResource(0)
        binding.tvShared.setTextColor(ContextCompat.getColor(this, keyless.feature.common.R.color.black))
        binding.tvAccess.setTextColor(ContextCompat.getColor(this, keyless.feature.common.R.color.white))
        val typeface = ResourcesCompat.getFont(this, keyless.data.utils.android.R.font.poppins_medium_500)
        binding.tvAccess.typeface = typeface
        val typeface1 = ResourcesCompat.getFont(this, keyless.data.utils.android.R.font.poppins_regular_400)
        binding.tvShared.typeface = typeface1
    }

    private fun setUpAccessRecyclerView() {
        binding.rvAccess.layoutManager = layoutManagerAccess
        adapterAccess = AccessAdapter()
        binding.rvAccess.adapter = adapterAccess
    }

    private fun setUpSharedRecyclerView() {
        binding.rvShared.layoutManager = layoutManagerShared
        adapterShared = SharedAdapter()
        binding.rvShared.adapter = adapterShared
    }

}