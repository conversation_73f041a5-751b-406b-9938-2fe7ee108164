package feature.dashboard.shareaccess

import android.annotation.SuppressLint
import android.app.Activity
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import core.lock.ttlock.AndroidTTLockManager
import core.lock.ttlock.TTLockManager
import core.lock.ttlock.models.AndroidTTLock
import data.network.android.LocksListResponse
import data.network.android.UserSharedModel
import data.network.android.models.RevokeResponse
import data.utils.android.CommonValues
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import feature.common.dialogs.OnActionYesNo
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.appToast
import feature.common.dialogs.dialogYesNo
import feature.common.dialogs.scanningDialog
import keyless.feature.common.R
import keyless.feature.dashboard.databinding.ActivityShareAccessMainBinding
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import org.koin.android.ext.android.inject
import kotlin.coroutines.cancellation.CancellationException
import kotlin.time.Duration.Companion.seconds

class ShareAccessMainActivity : AppCompatActivity(), AdapterAccessSharedWith.ClickOnDelete {

    private val ttlock: TTLockManager by inject()
    private var listUsers: ArrayList<UserSharedModel> = ArrayList()
    private lateinit var adapterSharedAccess: AdapterAccessSharedWith
    private lateinit var lockDetails: LocksListResponse.LocksModel
    lateinit var mViewModel: ShareAccessViewModel
    private lateinit var binding: ActivityShareAccessMainBinding
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityShareAccessMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setAdapter()
        initz()
        clickListeners()
        observerInit()
    }

    private fun observerInit() {
        mViewModel.progress.observe(this) {
            try {
                if (it) {
                    ProgressDialogUtils.getInstance().hideProgress()
                    ProgressDialogUtils.getInstance().showProgress(this, true)
                } else {
                    ProgressDialogUtils.getInstance().hideProgress()
                }
            } catch (_: Exception) {
            }
        }

        mViewModel.error.observe(this) {
            if (it == getString(keyless.feature.common.R.string.you_are_in_offline)) {
                binding.noInternetLayout.visibility = View.VISIBLE
                binding.textView13.visibility = View.GONE
            } else {
                toast(it)
            }
        }
    }

    private fun clickListeners() {
        binding.ivAddAccess.setOnClickListener {
            if (CommonValues.isNetworkAvailable(this)) {
                startActivityForResult(
                    Intent(this, AddUserActivity::class.java).putExtra(
                        "lockDetails",
                        lockDetails
                    ),
                    14
                )
            } else {
                toast(getString(keyless.feature.common.R.string.you_are_in_offline))
            }
        }

        binding.viewBack.setOnClickListener {
            finish()
        }

        binding.addUserBtn.setOnClickListener {
            if (CommonValues.isNetworkAvailable(this)) {
                binding.ivAddAccess.callOnClick()
            } else {
                toast(getString(keyless.feature.common.R.string.you_are_in_offline))
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            apiImplementation()
        }
    }

    private fun setAdapter() {
        binding.rvAccessMain.layoutManager = LinearLayoutManager(this)
        adapterSharedAccess = AdapterAccessSharedWith(this)
        binding.rvAccessMain.adapter = adapterSharedAccess
    }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[ShareAccessViewModel::class.java]
        lockDetails = intent.getParcelableExtra("lockDetails")!!
        binding.ivAddAccess.isVisible = lockDetails.lock.primary
        binding.addUserBtn.isVisible = lockDetails.lock.primary
        apiImplementation()
    }

    private fun apiImplementation() {
        mViewModel.getShareAccessList(sharePrefs.token, lockDetails.lock.unique_key).observe(this) {
            if (it.users.size > 0) {
                binding.textView13.visibility = View.VISIBLE
                binding.noDataView.isVisible = false
                binding.addUserBtn.isVisible = false
                binding.noInternetLayout.isVisible = false
                binding.rvAccessMain.isVisible = true
                listUsers = it.users
                adapterSharedAccess.updateData(it.users)
            } else {
                binding.noDataView.isVisible = true
                binding.addUserBtn.isVisible = lockDetails.lock.primary
                binding.rvAccessMain.isVisible = false
                binding.textView13.isVisible = false
            }
        }
    }

    override fun clickForDelete(model: UserSharedModel) {
        if (model.status == "pending") {
            dialogYesNo(
                this,
                getString(R.string.text_cancel),
                getString(keyless.data.utils.android.R.string.want_to_cancel_invitation),
                object : OnActionYesNo {
                    override fun onYes(view: View) {
                        mViewModel.cancelAccess(
                            sharePrefs.token,
                            model.detail.id
                        ).observe(this@ShareAccessMainActivity) {
                            if (it.success) {
                                apiImplementation()
                            }
                        }
                    }

                    override fun onNo(view: View) {
                    }

                    override fun onClickData(view: View, data: String) {
                    }
                }
            )
        } else {
            var urn = 0
            if (model.detail.urn_id?.isNotEmpty()!!) {
                urn = model.detail.urn_id?.toInt()!!
            } else {
                urn = 0
            }
            val request = RevokeResponse(
                lockType = lockDetails.lock.provider,
                locks = arrayListOf(lockDetails.lock.unique_key),
                urn = urn,
                userId = model.detail._id,
                assignment_id = model.assignment_data._id
            )
            dialogYesNo(
                this,
                getString(keyless.data.utils.android.R.string.revoke),
                getString(keyless.data.utils.android.R.string.want_to_revoke_lock),
                object : OnActionYesNo {
                    override fun onYes(view: View) {
                        deleteAccess(model, request)
                    }

                    override fun onNo(view: View) {
                    }

                    override fun onClickData(view: View, data: String) {
                    }
                }
            )
        }
    }

    override fun clickForEdit(detail: UserSharedModel) {
        if (detail.status != "pending") {
            startActivityForResult(
                Intent(this, AddUserActivity::class.java).putExtra(
                    "lockDetails",
                    lockDetails
                ).putExtra("detailForEdit", detail),
                14
            )
        }
    }

    private fun deleteAccess(model: UserSharedModel, request: RevokeResponse) {
        val manager = ttlock as AndroidTTLockManager

        if (!isBluetoothEnabled()) {
            askUserToEnableBluetoothIfNeeded()
            return
        }

        if (model.assignment_data.passcode.isEmpty() || !lockDetails.ttlockOfflinePasscode()) {
            deleteAccessApi(model = model, request = request)
            return
        }

        var found = false
        var job: Job? = null
        val dialog = scanningDialog(onCancel = { job?.cancel() })
        job = lifecycleScope.launch {
            kotlin.runCatching {
                manager.stream.first { it.find { it.name == lockDetails.lock.unique_key } != null }
                found = true
                AndroidTTLock.deletePasscode(
                    passcode = model.assignment_data.passcode,
                    lockData = lockDetails.lock.access_key
                )
                deleteAccessApi(model, request)

                runOnUiThread { kotlin.runCatching { dialog.dismiss() } }
            }.onFailure {
                if (it is CancellationException) return@launch
                runOnUiThread { kotlin.runCatching { dialog.dismiss() } }
                runOnUiThread { appToast(it.message ?: "Error happened") }
            }
        }

        lifecycleScope.launch {
            delay(20.seconds)
            runOnUiThread { kotlin.runCatching { dialog.dismiss() } }
            if (!found) job.cancel()
        }
    }

    private fun deleteAccessApi(model: UserSharedModel, request: RevokeResponse) {
        mViewModel.deleteAccess(
            sharePrefs.token,
            request
        ).observe(this@ShareAccessMainActivity) {
            mViewModel.hitLogApi(
                sharePrefs.token,
                lockDetails.lock._id,
                model.detail._id,
                CommonValues.getDeviceNameApi(),
                sharePrefs.uuid,
                "0",
                model.assignment_data.valid_from,
                model.assignment_data.valid_to,
                model.assignment_data._id
            ).observe(this@ShareAccessMainActivity) {
                apiImplementation()
            }
        }
    }
}

@SuppressLint("MissingPermission")
fun Activity.askUserToEnableBluetoothIfNeeded() {
    val bluetoothManager: BluetoothManager = getSystemService(BluetoothManager::class.java)
    val bluetoothAdapter: BluetoothAdapter? = bluetoothManager.getAdapter()

    if (bluetoothAdapter?.isEnabled == false) {
        val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
        startActivityForResult(enableBtIntent, 5)
    }
}

fun Context.isBluetoothEnabled(): Boolean {
    val bluetoothManager: BluetoothManager = getSystemService(BluetoothManager::class.java)
    val bluetoothAdapter: BluetoothAdapter? = bluetoothManager.getAdapter()

    return bluetoothAdapter?.isEnabled == true
}