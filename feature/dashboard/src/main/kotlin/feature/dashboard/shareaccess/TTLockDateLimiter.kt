package feature.dashboard.shareaccess

import android.os.Parcel
import android.os.Parcelable
import com.wdullaer.materialdatetimepicker.date.DateRangeLimiter
import data.utils.android.datetime.isInTTLockPasscodeRange
import data.utils.android.datetime.nextTTLockPasscode
import data.utils.android.datetime.toDate
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.Calendar
import java.util.Date


internal class TTLockDateLimiter() : DateRangeLimiter, Parcelable {

    private var initYear: Int? = null
    private var initCalendar: Calendar? = null

    constructor(parcel: Parcel) : this()

    constructor(initYear: Int, initCalendar: Calendar) : this() {
        this.initYear = initYear
        this.initCalendar = initCalendar
    }

    override fun getMinYear(): Int = initYear ?: 2000
    override fun getMaxYear(): Int =  endDate.get(Calendar.YEAR)

    override fun getStartDate(): Calendar {
        return initCalendar!!
    }

    override fun getEndDate(): Calendar {
        val now = startDate
        val output: Calendar = now.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusYears(3).toCalendar()
        return output
    }

    public override fun isOutOfRange(year: Int, month: Int, day: Int): Boolean {
        return !LocalDateTime.of(year, month + 1, day, 0, 0).isInTTLockPasscodeRange(startDate.toDate())
    }

    override fun setToNearestDate(day: Calendar): Calendar {
        return if(day.isInTTLockPasscodeRange(startDate.toDate())) day else day.nextTTLockPasscode(startDate)
    }

    override fun writeToParcel(dest: Parcel, flags: Int) {

    }

    public override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<TTLockDateLimiter> {
        override fun createFromParcel(parcel: Parcel): TTLockDateLimiter {
            return TTLockDateLimiter(parcel)
        }

        override fun newArray(size: Int): Array<TTLockDateLimiter?> {
            return arrayOfNulls(size)
        }
    }

    private fun LocalDate.toCalendar(): Calendar {
        val zonedDateTime: ZonedDateTime = this.atStartOfDay(ZoneId.systemDefault())
        val instant: Instant? = zonedDateTime.toInstant()
        val date: Date? = Date.from(instant)
        val calendar = Calendar.getInstance()
        calendar.time = date!!
        return calendar
    }
}