package feature.dashboard.moreinfo

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import core.locks.logs.models.LockLogActionType
import data.common.preferences.Preferences
import data.network.android.LocksListResponse
import data.network.android.log
import data.network.android.models.MoreInfoModel
import data.utils.android.CommonValues
import data.utils.android.clickWithDebounce
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import data.utils.android.settings.SharedPreferenceUtils
import feature.dashboard.lockhistory.LockHistoryActivity
import feature.dashboard.lockinfo.LockInfoActivity
import feature.dashboard.locksettings.LockSettingsActivity
import feature.dashboard.locksettings.LockSettingsActivity.Companion.LOCK_ACCESS_KEY
import feature.dashboard.locksettings.LockSettingsActivity.Companion.LOCK_MODEL_KEY
import feature.dashboard.locksettings.LockSettingsActivity.Companion.LOCK_PROVIDER_KEY
import feature.dashboard.shareaccess.ShareAccessMainActivity
import keyless.data.utils.android.R
import keyless.feature.dashboard.databinding.MoreInfoAdapterLayoutBinding

class MoreInfoAdapter(
    var list: ArrayList<MoreInfoModel>,
    var context: Context,
    var lockDetails: LocksListResponse.LocksModel
) :
    RecyclerView.Adapter<MoreInfoAdapter.ViewHolder>() {

    var listener = context as ClickOnPrivacy
    lateinit var contextMain: Context
    var lockPrivacy = false

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        contextMain = viewGroup.context
        val binding = MoreInfoAdapterLayoutBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(list[position])
    }

    override fun getItemCount() = list.size
    fun updateSwitch() {
        lockDetails.lock.privacy_mode = false
        notifyDataSetChanged()
    }

    inner class ViewHolder(val binding: MoreInfoAdapterLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: MoreInfoModel) {
            binding.tvMoreInfo.text = list[position].name
            if (list[position].name == context.getString(keyless.data.utils.android.R.string.text_lock_privacy)) {
                binding.simpleSwitch.visibility = View.VISIBLE
                binding.infoBtn.visibility = View.VISIBLE
                binding.icon.visibility = View.GONE
//            val privacyChanged = lockDetails.privacy_changed
            } else {
                binding.infoBtn.visibility = View.GONE
                binding.simpleSwitch.visibility = View.GONE
                binding.icon.visibility = View.VISIBLE
            }
            binding.infoBtn.setOnClickListener {
                defaultDialog(
                    context,
                    CommonValues.LOCK_INFO_MSG(context),
                    object : OnActionOK {
                        override fun onClickData() {
                        }
                    }
                )
            }

            binding.mainLay.clickWithDebounce {
                when (list[position].id) {
                    0 -> {
                        listener.openServicesSheet(lockDetails, false)
                    }

                    1 -> {
                        val intent = Intent(context, LockHistoryActivity::class.java).putExtra(
                            "id",
                            lockDetails.lock._id
                        )
                        context.startActivity(intent)
                    }

                    2 -> {
                        val intent = Intent(context, LockInfoActivity::class.java).putExtra(
                            "lockDetails",
                            lockDetails.lock._id
                        )
                        context.startActivity(intent)
                    }

                    4 -> {
                        val intent = Intent(context, ShareAccessMainActivity::class.java).putExtra(
                            "lockDetails",
                            lockDetails
                        )
                        context.startActivity(intent)
                    }

                    5 -> {
                        listener.openServicesSheet(lockDetails, true)
                    }

                    500 -> {
                        defaultDialog(
                            context,
                            lockDetails.passcode,
                            object : OnActionOK {
                                override fun onClickData() {
                                }
                            })
                    }

                    501 -> {
                        val intent = Intent(context, LockSettingsActivity::class.java)
                            .putExtra(LOCK_PROVIDER_KEY, lockDetails.lock.provider)
                            .putExtra(
                                LOCK_ACCESS_KEY,
                                CommonValues.decrypt(
                                    lockDetails.lock.encrypted_key,
                                    SharedPreferenceUtils.getInstance(context).uuid
                                )
                            )
                            .putExtra(LOCK_MODEL_KEY, lockDetails.lock.unique_key.split("-").first().trim())

                        context.startActivity(intent)
                    }
                }
            }

            binding.simpleSwitch.isChecked = lockDetails.lock.privacy_mode
            lockPrivacy = lockDetails.lock.privacy_mode

            binding.simpleSwitch.setOnCheckedChangeListener { buttonView, isChecked ->
                if (binding.simpleSwitch.isPressed) {
                    if (Preferences.isAdminLogin()) {
                        defaultDialog(
                            context,
                            context.getString(R.string.disabled_in_admin_mode),
                            object : OnActionOK {
                                override fun onClickData() {
                                }
                            }
                        )
                        binding.simpleSwitch.isChecked = lockPrivacy
                    } else {
                        if (!lockDetails.lock.privacy_permission && lockPrivacy) {
                            binding.simpleSwitch.isChecked = lockPrivacy
                            defaultDialog(
                                contextMain,
                                context.getString(
                                    R.string.dont_have_permission_to_change_lock_privacy
                                ),
                                object :
                                    OnActionOK {
                                    override fun onClickData() {
                                    }
                                }
                            )

                            lockDetails.lock.log(
                                action = LockLogActionType.Unlock,
                                message = "No permission to change lock privacy",
                                data = ""
                            )
                        } else {
                            if (lockPrivacy) {
                                listener.apiLockPrivacy("off")
                                lockPrivacy = false
                            } else {
                                listener.apiLockPrivacy("on")
                                lockPrivacy = true
                            }
                        }
                    }
                }
            }
        }
    }

//    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
//        var icon: ImageView
//        var switch: SwitchCompat
//        var tvMoreInfo: TextView
//
//        init {
//            icon = view.findViewById(R.id.icon)
//            tvMoreInfo = view.findViewById(R.id.tv_more_info)
//            switch = view.findViewById(R.id.simpleSwitch)
//        }
//    }

    interface ClickOnPrivacy {
        fun apiLockPrivacy(status: String)
        fun openServicesSheet(lockDetails: LocksListResponse.LocksModel, isSupport: Boolean)
    }
}