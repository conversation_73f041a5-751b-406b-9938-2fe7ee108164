plugins {
    id("keyless.android.feature")
}

android {
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

dependencies {
    implementation(project(":data-common"))
    implementation(project(":data-network-android"))
    implementation(project(":data-utils-android"))
    implementation(project(":feature-common"))

    implementation(keyless.androidx.lifecycle.livedata)
    implementation(keyless.retrofit)

    implementation("com.mikhaellopez:circularimageview:4.3.1")
    implementation("org.greenrobot:eventbus:3.3.1")
    implementation ("com.github.mukeshsolanki:android-otpview-pinview:3.2.0")
    implementation ("com.github.khoyron:Actionsheet-android:4")
}