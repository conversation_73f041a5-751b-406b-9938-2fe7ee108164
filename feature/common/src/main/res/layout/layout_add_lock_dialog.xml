<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    card_view:cardCornerRadius="10dp"
    card_view:cardElevation="4dp">


    <androidx.cardview.widget.CardView
        android:id="@+id/cardView2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="15dp"
        card_view:cardCornerRadius="10dp"
        card_view:cardElevation="0dp"
        card_view:cardUseCompatPadding="true"
        card_view:layout_constraintBottom_toBottomOf="parent"
        card_view:layout_constraintEnd_toEndOf="parent"
        card_view:layout_constraintStart_toStartOf="parent"
        card_view:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical">

            <!--            <TextView-->
            <!--                android:id="@+id/tv_groceries"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginTop="20dp"-->
            <!--                android:layout_marginBottom="20dp"-->
            <!--                android:fontFamily="@font/poppins_medium_500"-->
            <!--                android:gravity="center"-->
            <!--                android:text=""-->
            <!--                android:textColor="@color/black"-->
            <!--                android:textSize="18sp" />-->

            <androidx.cardview.widget.CardView
                android:id="@+id/cvAddLock"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="13dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="13dp"
                card_view:cardCornerRadius="10dp"
                card_view:cardElevation="6dp"
                card_view:cardUseCompatPadding="true">

                <TextView
                    android:id="@+id/tv_instashop"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/poppins_medium_500"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:padding="14dp"
                    android:text="@string/add_lock"
                    android:textColor="@color/black"
                    android:textSize="14sp" />
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cvMasterKey"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="13dp"
                android:layout_marginEnd="13dp"
                card_view:cardCornerRadius="10dp"
                card_view:cardElevation="6dp"
                card_view:cardUseCompatPadding="true">


                <TextView
                    android:id="@+id/tv_noon_daily"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/poppins_medium_500"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:padding="14dp"
                    android:text="@string/add_master_key"
                    android:textColor="@color/black"
                    android:textSize="14sp" />
            </androidx.cardview.widget.CardView>


            <androidx.cardview.widget.CardView
                android:id="@+id/cvConfigureCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="13dp"
                android:layout_marginEnd="13dp"
                android:layout_marginBottom="20dp"
                card_view:cardCornerRadius="10dp"
                card_view:cardElevation="6dp"
                card_view:cardUseCompatPadding="true">


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/poppins_medium_500"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:padding="14dp"
                    android:text="@string/configure_card"
                    android:textColor="@color/black"
                    android:textSize="14sp" />
            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <ImageView
        android:id="@+id/closeDialog"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/iv_cross_bg_white"
        android:elevation="50dp"
        android:src="@drawable/iv_cross_black"
        card_view:layout_constraintBottom_toBottomOf="@+id/cardView2"
        card_view:layout_constraintEnd_toEndOf="parent"
        card_view:layout_constraintHorizontal_bias="1.0"
        card_view:layout_constraintStart_toStartOf="parent"
        card_view:layout_constraintTop_toTopOf="parent"
        card_view:layout_constraintVertical_bias="0.0" />

</androidx.constraintlayout.widget.ConstraintLayout>
