package feature.common.device

import android.annotation.SuppressLint
import android.content.Context
import core.lock.common.toLocalDateTime
import data.common.preferences.Preferences
import data.utils.android.CommonValues
import data.utils.android.settings.SharedPreferenceUtils
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.time.Duration
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.Locale
import java.util.TimeZone


object Datetime {

    fun fromString(date: String): Date? {
        return try {
            val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
            parser.timeZone = TimeZone.getTimeZone("UTC")
            parser.parse(date)
        } catch (e: Exception) {
            null
        }
    }

    fun formattedDateOnlyEn(validFrom: String): String {
        return try {
            val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
            parser.timeZone = TimeZone.getTimeZone("UTC")
            val value: Date = parser.parse(validFrom)
            val outputFormatter1: DateFormat = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
            outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
            outputFormatter1.format(value)
        } catch (e: Exception) {
            ""
        }
    }


    fun formatDdMmYyyyy(validFrom: String, context: Context): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
        parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(validFrom)
        val outputFormatter1: DateFormat = SimpleDateFormat(
            "dd-MM-yyyy hh:mm a",
            Locale(SharedPreferenceUtils.getInstance(context).language!!)
        )
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())

        return outputFormatter1.format(value)
    }


    fun formatTimeDate(validFrom: String, context: Context): String {
        return try {
            val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
            parser.timeZone = TimeZone.getTimeZone("UTC")
            val value: Date = parser.parse(validFrom)
            val outputFormatter1: DateFormat =
                SimpleDateFormat(CommonValues.DATE_FORMAT, Locale(SharedPreferenceUtils.getInstance(context).language!!))
            outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
            outputFormatter1.format(value)
        } catch (e: Exception) {
            ""
        }
    }

    fun formatTimeDateSimple(validFrom: String): String {
        return try {
            val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
            parser.timeZone = TimeZone.getTimeZone("UTC")
            val value: Date = parser.parse(validFrom)
            val outputFormatter1: DateFormat = SimpleDateFormat(CommonValues.DATE_FORMAT)
            outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
            outputFormatter1.format(value)
        } catch (e: Exception) {
            ""
        }
    }


    fun formatOnlyDate(validFrom: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd", Locale("en"))
        parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(validFrom)
        val outputFormatter1: DateFormat = SimpleDateFormat("dd/MM/yyyy", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }


    fun formatOnlyDateWithoutTimeZone(validFrom: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
//            parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(validFrom)
        val outputFormatter1: DateFormat = SimpleDateFormat("dd/MM/yyyy", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)

    }


    fun formatCompare(validFrom: String): String {
        val parser = SimpleDateFormat("dd-MMM-yyyy HH:mm", Locale("en"))
        parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(validFrom)
        val outputFormatter1: DateFormat =
            SimpleDateFormat("dd-MM-yyyy HH:mm", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())

        return outputFormatter1.format(value)
    }


    fun formatTimeDate1(validFrom: String, context: Context): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
        parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(validFrom)
        val outputFormatter1: DateFormat =
            SimpleDateFormat("dd MMM, yy", Locale(SharedPreferenceUtils.getInstance(context).language!!))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }


    fun formatDateEdit(validFrom: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ", Locale("en"))
//            parser.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        val value: Date = parser.parse(validFrom)
        val outputFormatter1: DateFormat = SimpleDateFormat("dd-MMM-yyyy HH:mm", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }


    @SuppressLint("SimpleDateFormat")
    fun dateForLockHistory(): String {
        val currentDate = Date()
        val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ", Locale("en"))
        sdf.timeZone = TimeZone.getTimeZone(Preferences.timeZoneOffset.get())
        return sdf.format(currentDate)
    }


    fun dateFor(dateMain: String): String {
        val df: DateFormat = SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss", Locale("en"))
        df.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = df.parse(dateMain)
        val outputFormatter1: DateFormat = SimpleDateFormat("yyyy-MM-dd", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }

    fun dateForWithoutTimeZone(dateMain: String): String {
        val df: DateFormat = SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss", Locale("en"))
        val value: Date = df.parse(dateMain)
        val outputFormatter1: DateFormat = SimpleDateFormat("yyyy-MM-dd", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }


    fun dateForHour(dateMain: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
        parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(dateMain)
        val outputFormatter1: DateFormat = SimpleDateFormat("HH", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }

    fun dateForHourWithoutTimezone(dateMain: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
        val value: Date = parser.parse(dateMain)
        val outputFormatter1: DateFormat = SimpleDateFormat("HH", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }


    fun dateForMin(dateMain: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
        parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(dateMain)
        val outputFormatter1: DateFormat = SimpleDateFormat("mm", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }


    fun dateForMinWithoutTimeZone(dateMain: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
        val value: Date = parser.parse(dateMain)
        val outputFormatter1: DateFormat = SimpleDateFormat("mm", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())

        return outputFormatter1.format(value)
    }


    fun dateZformat(dateMain: String, context: Context): String {
        val df: DateFormat = SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss.SSS'Z'", Locale("en"))
        df.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = df.parse(dateMain)
        val outputFormatter1: DateFormat =
            SimpleDateFormat("dd-MMM-yyyy hh:mm a", Locale(SharedPreferenceUtils.getInstance(context).language!!))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }
}