package feature.regula.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class OneCandidate(

    @SerializedName("AuthenticityNecessaryLights") var AuthenticityNecessaryLights: Int? = null,
    @SerializedName("CheckAuthenticity") var CheckAuthenticity: Int? = null,
    @SerializedName("DocumentName") var DocumentName: String? = null,
    @SerializedName("FDSIDList") var FDSIDList: FDSIDList? = FDSIDList(),
    @SerializedName("ID") var ID: Int? = null,
    @SerializedName("NecessaryLights") var NecessaryLights: Int? = null,
    @SerializedName("OVIExp") var OVIExp: Int? = null,
    @SerializedName("P") var P: Double? = null,
    @SerializedName("RFID_Presence") var RFIDPresence: Int? = null,
    @SerializedName("Rotated180") var Rotated180: Int? = null,
    @SerializedName("RotationAngle") var RotationAngle: Int? = null,
    @SerializedName("UVExp") var UVExp: Int? = null

)