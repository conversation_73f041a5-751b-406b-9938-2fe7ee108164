package feature.regula

import android.os.Bundle
import android.util.Log
import android.widget.ImageView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.regula.facesdk.FaceSDK
import com.regula.facesdk.exception.InitException
import com.regula.facesdk.model.results.FaceCaptureResponse
import keyless.feature.regula.databinding.ActivityFaceCameraBinding

class FaceCameraActivity : AppCompatActivity() {

    lateinit var binding: ActivityFaceCameraBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFaceCameraBinding.inflate(layoutInflater)
        setContentView(binding.root)
        FaceSDK.Instance().init(this) { status: Boolean, e: InitException? ->
            if (!status) {
                Toast.makeText(
                    this@FaceCameraActivity,
                    "Init finished with error: " + if (e != null) e.message else "",
                    Toast.LENGTH_LONG
                ).show()
                return@init
            } else {
//                openCamera()
            }
            Log.d("MainActivity", "FaceSDK init completed successfully")
        }

//        setContentView(R.layout.activity_face_camera)
    }

    private fun openCamera() {
        startFaceCaptureActivity(binding.imageView1)
    }

    private fun startFaceCaptureActivity(imageView: ImageView?) {
//        val configuration = FaceCaptureConfiguration.Builder().setCameraSwitchEnabled(true).build()

//        Handler(Looper.getMainLooper()).postDelayed({
        FaceSDK
            .Instance()
            .presentFaceCaptureActivity(this@FaceCameraActivity) { faceCaptureResponse: FaceCaptureResponse? ->
                if (faceCaptureResponse?.image != null) {
                    imageView!!.setImageBitmap(faceCaptureResponse.image!!.bitmap)

//                setGroupSelection(ImageType.LIVE)
                }
            }
//        },2000)
    }
}