package feature.regula

import android.app.Dialog
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.util.Log
import android.view.Window
import android.view.WindowManager
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import data.network.android.LocksListResponse
import data.utils.android.settings.SharedPreferenceUtils
import feature.regula.model.DocIntentModel
import keyless.feature.regula.R
import keyless.feature.regula.databinding.ActivityDocResultBinding
import org.json.JSONArray

class DocResultActivity : AppCompatActivity() {

    private lateinit var receivedJsonArray: JSONArray
    private var model: DocIntentModel = DocIntentModel()
    private var lockDetails: LocksListResponse.LocksModel = LocksListResponse.LocksModel()
    private var valueBtn: String = "0"
    lateinit var binding: ActivityDocResultBinding
    private var statusApi: String = ""
    lateinit var mViewModel: RegulaViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDocResultBinding.inflate(layoutInflater)
        setContentView(binding.root)
        mViewModel = ViewModelProvider(this)[RegulaViewModel::class.java]
        setData()
        clickListeners()
    }

    private fun clickListeners() {
        binding.txtBtnRetry.setOnClickListener {
            setResult(190)
            finish()
        }

        binding.txtBtnComplete.setOnClickListener {
            if (valueBtn == "0") {
                setResult(190)
                finish()
            } else {
                binding.progressLay.isVisible = true
                mViewModel.checkInComplete(
                    model.name!!,
                    lockDetails.booking_number,
                    model.documentNumber,
                    statusApi,
                    model.documentType!!,
                    model.identity1,
                    model.identity2,
                    model.documentImageApi, // portrait
                    model.myImageApi, // captured
                    model.similarity, sharePrefs.token, receivedJsonArray,
                    normalizeDate(model.expiryDate!!)
                ).observe(this) {
                    binding.progressLay.isVisible = false
                    if (it.success) {
                        showDialogForScreen()
                    } else {
                        Toast.makeText(this, it.message, Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }

        binding.viewBack.setOnClickListener {
            setResult(190)
            finish()
        }
    }

    private fun showDialogForScreen() {
        val dialog = Dialog(this)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.setBackgroundDrawableResource(keyless.feature.common.R.drawable.white_all_corners_10)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.dialog_doc_scan_done)
        val doneBtn = dialog.findViewById<TextView>(R.id.doneBtn)
        val txtAddGuest = dialog.findViewById<TextView>(R.id.txtAddGuest)
        dialog.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialog.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
            val maxWidth = displayRectangle.width() * 0.8f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight)
        }

        txtAddGuest.setOnClickListener {
            dialog.dismiss()
            startActivityForResult(Intent(this, ScanActivity::class.java).putExtra("lockDetails", lockDetails), 50)
            finish()
        }

        doneBtn.setOnClickListener {
            dialog.dismiss()
            setResult(60)
            finish()
        }

        dialog.show()
    }

    private fun setData() {
        val receivedJsonArrayString = intent.getStringExtra("jsonArray")
        receivedJsonArray = JSONArray(receivedJsonArrayString)
        model = intent.getParcelableExtra("model")!!
        lockDetails = intent.getParcelableExtra("lockDetails")!!
        Log.e("similarity", model.similarity.toString())
        binding.nameTxt.text = model.name
        binding.docType.text = model.docType
        binding.expDate.text = model.docExpiry
        binding.imgDocument.setImageBitmap(model.docSelfie)
        binding.imageCamera.setImageBitmap(DocScanActivity.modell.bitmap)

        if (model.similarity?.toDouble()!! >= 0.75) {
            valueBtn = "2"
            binding.faceMatch.setImageResource(R.drawable.ic_face_match_success)
            binding.picStatus.setImageResource(R.drawable.ic_pic_matched)
            binding.txtBtnComplete.isVisible = true
            binding.btnLay.isVisible = false
            binding.txtBtnComplete.text = getString(keyless.data.utils.android.R.string.complete_check_in)
            binding.textStatus.text = getString(keyless.data.utils.android.R.string.your_check_in_has_been_approved)
            statusApi = "1"
        } else if (model.similarity?.toDouble()!! > 0.30 && model.similarity?.toDouble()!! < 0.75) {
            valueBtn = "1"
            binding.faceMatch.setImageResource(R.drawable.ic_partial_match)
            binding.picStatus.setImageResource(R.drawable.ic_partial_tick)
            binding.txtBtnComplete.isVisible = false
            binding.btnLay.isVisible = true
            binding.textStatus.text =
                getString(keyless.data.utils.android.R.string.your_check_in_will_be_submitted_for_approval)
            statusApi = "0"
        } else {
            valueBtn = "0"
            statusApi = "3"
            binding.faceMatch.setImageResource(R.drawable.ic_face_match_error)
            binding.picStatus.setImageResource(R.drawable.ic_pic_error)
            binding.txtBtnComplete.isVisible = true
            binding.btnLay.isVisible = false
            binding.txtBtnComplete.text = getString(keyless.data.utils.android.R.string.retry)
            binding.textStatus.text = getString(keyless.data.utils.android.R.string.your_check_in_has_failed)
        }
    }
}