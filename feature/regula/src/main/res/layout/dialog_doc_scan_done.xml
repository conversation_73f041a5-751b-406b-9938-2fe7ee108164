<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <TextView
        android:id="@+id/textView33"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:text="@string/add_guest"
        android:textColor="@color/black"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView35"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="20dp"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:text="@string/want_another_check_in"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView33" />


    <View
        android:id="@+id/view4"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:background="@color/line_bg_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView35" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline6"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />


    <TextView
        android:id="@+id/txtAddGuest"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:text="@string/add_guest_btn"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/guideline6"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view4"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/doneBtn"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:text="@string/done"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/guideline6"
        app:layout_constraintTop_toBottomOf="@+id/view4"
        app:layout_constraintVertical_bias="0.0" />


    <View
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:background="@color/line_bg_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/guideline6"
        app:layout_constraintStart_toStartOf="@+id/guideline6"
        app:layout_constraintTop_toBottomOf="@+id/view4" />


</androidx.constraintlayout.widget.ConstraintLayout>