<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/regula_nav_graph"
        app:startDestination="@id/documentVerificationFragment">

    <fragment
            android:id="@+id/documentVerificationFragment"
            android:name="feature.regula.refactored.documents.DocumentVerificationFragment"
            android:label="CheckInFragment" >
        <action
                android:id="@+id/action_document_to_face"
                app:destination="@id/faceVerificationFragment" />
    </fragment>
    <fragment
            android:id="@+id/faceVerificationFragment"
            android:name="feature.regula.refactored.face.FaceVerificationFragment"
            android:label="FaceVerificationFragment" >
        <action
                android:id="@+id/action_face_to_results"
                app:destination="@id/checkInResultsFragment" />
    </fragment>
    <fragment
            android:id="@+id/checkInResultsFragment"
            android:name="feature.regula.refactored.checkin.CheckInResultsFragment"
            android:label="CheckInResultsFragment" />
</navigation>