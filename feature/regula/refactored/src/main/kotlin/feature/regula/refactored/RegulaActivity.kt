package feature.regula.refactored

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import keyless.feature.regula.refactored.R
import org.koin.androidx.viewmodel.ext.android.viewModel

class RegulaActivity : AppCompatActivity() {

    private val viewModel: RegulaActivityViewModel by viewModel()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_regula)
        // Init view model
        viewModel
    }

    override fun onDestroy() {
        viewModel.onDestroy()
        super.onDestroy()
    }
}