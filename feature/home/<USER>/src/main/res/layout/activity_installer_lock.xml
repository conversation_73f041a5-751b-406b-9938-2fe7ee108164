<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent">


    <ImageView
        android:id="@+id/backBtn"
        style="@style/ImageMirror"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:padding="20dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/titlePage"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="32dp"
        android:ellipsize="end"
        android:fontFamily="@font/poppins_bold_700"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/home"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/frameContainerHome"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="15dp"
        android:background="@drawable/white_top_corners"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtn"
        app:layout_constraintVertical_bias="0.0">

        <TextView
            android:id="@+id/textView36"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="20dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:includeFontPadding="false"
            android:text="@string/installation_details"
            android:textColor="@color/black"
            android:textSize="22dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txtDate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:text="22-July-2023"
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView36" />

        <TextView
            android:id="@+id/txtSlot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:text="Morning (9AM to 2PM)"
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintEnd_toEndOf="@+id/textView36"
            app:layout_constraintTop_toTopOf="@+id/txtDate" />


        <TextView
            android:id="@+id/txtLockSizeColor"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:text="ISEO  (35mm x 35mm Black color)"
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintEnd_toEndOf="@+id/textView36"
            app:layout_constraintStart_toStartOf="@+id/txtDate"
            app:layout_constraintTop_toBottomOf="@+id/txtDate" />


        <TextView
            android:id="@+id/txtTotalLocks"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:text="@string/total_locks"
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintEnd_toEndOf="@+id/textView36"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="@+id/txtDate"
            app:layout_constraintTop_toBottomOf="@+id/txtLockSizeColor" />


        <TextView
            android:id="@+id/numberTotalLocks"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_semibold_600"
            android:includeFontPadding="false"
            android:text="14"
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintEnd_toEndOf="@+id/textView36"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/txtTotalLocks"
            app:layout_constraintTop_toTopOf="@+id/txtTotalLocks" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:text="@string/pending_locks1"
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintEnd_toStartOf="@+id/numberPendingLocks"
            app:layout_constraintTop_toBottomOf="@+id/txtLockSizeColor" />


        <TextView
            android:id="@+id/numberPendingLocks"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_semibold_600"
            android:includeFontPadding="false"
            android:text="14"
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintEnd_toEndOf="@+id/txtLockSizeColor"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toEndOf="@+id/txtTotalLocks"
            app:layout_constraintTop_toTopOf="@+id/txtTotalLocks" />


        <TextView
            android:id="@+id/textView37"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="20dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:includeFontPadding="false"
            android:text="@string/location_details"
            android:textColor="@color/black"
            android:textSize="22dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtTotalLocks" />

        <TextView
            android:id="@+id/txtCompanyName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:text="Almuharak Security Services"
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView37" />

        <ImageView
            android:id="@+id/markIcon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="12dp"
            android:src="@drawable/marker"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtCompanyName"
            app:layout_constraintVertical_bias="0.0" />


        <TextView
            android:id="@+id/txtAddress"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="40dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:text="Ofc Number-1, Floor-4 Motiaz Business park, Zirakpur"
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintEnd_toStartOf="@+id/imgLocation"
            app:layout_constraintStart_toEndOf="@+id/markIcon"
            app:layout_constraintTop_toBottomOf="@+id/txtCompanyName" />

        <ImageView
            android:id="@+id/imgLocation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:src="@drawable/map_colored"
            app:layout_constraintBottom_toBottomOf="@+id/txtAddress"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/txtAddress" />


        <TextView
            android:id="@+id/txtNameTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_medium_500"
            android:includeFontPadding="false"
            android:text="Name: "
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtAddress" />

        <TextView
            android:id="@+id/txtName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:text="cjhcuhjic"
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintStart_toEndOf="@+id/txtNameTitle"
            app:layout_constraintTop_toBottomOf="@+id/txtAddress" />

        <TextView
            android:id="@+id/txtPhoneTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_medium_500"
            android:includeFontPadding="false"
            android:text="Phone No: "
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtNameTitle" />

        <TextView
            android:id="@+id/txtPhoneNumber"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:text="545445454054"
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintStart_toEndOf="@+id/txtPhoneTitle"
            app:layout_constraintTop_toBottomOf="@+id/txtName" />

        <TextView
            android:id="@+id/txtBuildingTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_medium_500"
            android:includeFontPadding="false"
            android:text="Building: "
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtPhoneTitle" />

        <TextView
            android:id="@+id/txtBuilding"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:text="545445454054"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintStart_toEndOf="@+id/txtBuildingTitle"
            app:layout_constraintTop_toBottomOf="@+id/txtPhoneNumber" />

        <TextView
            android:id="@+id/txtAreaTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_medium_500"
            android:includeFontPadding="false"
            android:text="Area: "
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtBuildingTitle" />

        <TextView
            android:id="@+id/txtArea"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:text="545445454054"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintStart_toEndOf="@+id/txtAreaTitle"
            app:layout_constraintTop_toBottomOf="@+id/txtBuilding" />

        <TextView
            android:id="@+id/txtUnitTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_medium_500"
            android:includeFontPadding="false"
            android:text="Unit: "
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtAreaTitle" />

        <TextView
            android:id="@+id/txtUnit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:text="545445454054"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintStart_toEndOf="@+id/txtUnitTitle"
            app:layout_constraintTop_toBottomOf="@+id/txtArea" />

        <ImageView
            android:id="@+id/imageView10"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:src="@drawable/call_installer"
            app:layout_constraintBottom_toBottomOf="@+id/txtPhoneNumber"
            app:layout_constraintEnd_toEndOf="@+id/imgLocation"
            app:layout_constraintTop_toTopOf="@+id/txtName" />


        <TextView
            android:id="@+id/lockInstallations"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:includeFontPadding="false"
            android:textColor="@color/black"
            android:textSize="15dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtUnit" />


        <TextView
            android:id="@+id/btnInstallLock"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/bg_btn_round"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/install_locks"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline5"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/btnAddProperty"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/bg_btn_round"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/add_building"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guideline5" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvLockList"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="8dp"
            android:layout_marginBottom="12dp"
            app:layout_constraintBottom_toTopOf="@+id/btnInstallLock"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/lockInstallations"
            app:layout_constraintVertical_bias="0.0" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />


        <!--        <View-->
        <!--            android:id="@+id/viewPhone"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="0dp"-->
        <!--            app:layout_constraintBottom_toBottomOf="@+id/txtPhoneNumber"-->
        <!--            app:layout_constraintEnd_toEndOf="@+id/imageView10"-->
        <!--            app:layout_constraintStart_toStartOf="@+id/txtPhoneNumber"-->
        <!--            app:layout_constraintTop_toTopOf="@+id/txtPhoneNumber" />-->


        <!--        <TextView-->
        <!--            android:id="@+id/contactInfo"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_marginStart="16dp"-->
        <!--            android:layout_marginTop="16dp"-->
        <!--            android:fontFamily="@font/poppins_semibold_600"-->
        <!--            android:text="@string/contact_info"-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="16dp"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toTopOf="parent" />-->


        <!--        <TextView-->
        <!--            android:id="@+id/txtName"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_marginStart="16dp"-->
        <!--            android:fontFamily="@font/poppins_medium_500"-->
        <!--            android:includeFontPadding="false"-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="14dp"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toBottomOf="@+id/contactInfo" />-->

        <!--        <TextView-->
        <!--            android:id="@+id/txtPhoneNumber"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_marginStart="16dp"-->
        <!--            android:fontFamily="@font/poppins_medium_500"-->
        <!--            android:includeFontPadding="false"-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="14dp"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toBottomOf="@+id/txtName" />-->

        <!--        <View-->
        <!--            android:id="@+id/viewPhone"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="0dp"-->
        <!--            app:layout_constraintBottom_toBottomOf="@+id/txtPhoneNumber"-->
        <!--            app:layout_constraintEnd_toEndOf="@+id/imageView10"-->
        <!--            app:layout_constraintStart_toStartOf="@+id/txtPhoneNumber"-->
        <!--            app:layout_constraintTop_toTopOf="@+id/txtPhoneNumber" />-->

        <!--        <ImageView-->
        <!--            android:id="@+id/imageView10"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="0dp"-->
        <!--            android:padding="5dp"-->
        <!--            android:src="@drawable/call_img"-->
        <!--            app:layout_constraintBottom_toBottomOf="@+id/txtPhoneNumber"-->
        <!--            app:layout_constraintStart_toEndOf="@+id/txtPhoneNumber"-->
        <!--            app:layout_constraintTop_toBottomOf="@+id/txtName" />-->


        <!--        <TextView-->
        <!--            android:id="@+id/lockInstallations"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_marginStart="16dp"-->
        <!--            android:layout_marginTop="24dp"-->
        <!--            android:fontFamily="@font/poppins_semibold_600"-->
        <!--            android:includeFontPadding="false"-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="15dp"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toBottomOf="@+id/txtPhoneNumber" />-->


        <!--        <TextView-->
        <!--            android:id="@+id/btnInstallLock"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="50dp"-->
        <!--            android:layout_marginStart="16dp"-->
        <!--            android:layout_marginEnd="8dp"-->
        <!--            android:layout_marginBottom="16dp"-->
        <!--            android:background="@drawable/bg_btn_round"-->
        <!--            android:fontFamily="@font/poppins_medium_500"-->
        <!--            android:gravity="center"-->
        <!--            android:includeFontPadding="false"-->
        <!--            android:text="@string/install_locks"-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="16dp"-->
        <!--            app:layout_constraintBottom_toBottomOf="parent"-->
        <!--            app:layout_constraintEnd_toStartOf="@+id/guideline5"-->
        <!--            app:layout_constraintStart_toStartOf="parent" />-->

        <!--        <TextView-->
        <!--            android:id="@+id/btnAddProperty"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="50dp"-->
        <!--            android:layout_marginStart="8dp"-->
        <!--            android:layout_marginEnd="16dp"-->
        <!--            android:layout_marginBottom="16dp"-->
        <!--            android:background="@drawable/bg_btn_round"-->
        <!--            android:fontFamily="@font/poppins_medium_500"-->
        <!--            android:gravity="center"-->
        <!--            android:includeFontPadding="false"-->
        <!--            android:text="@string/add_property"-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="16dp"-->
        <!--            app:layout_constraintBottom_toBottomOf="parent"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            app:layout_constraintStart_toStartOf="@+id/guideline5" />-->

        <!--        <androidx.recyclerview.widget.RecyclerView-->
        <!--            android:id="@+id/rvLockList"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="0dp"-->
        <!--            android:layout_marginStart="8dp"-->
        <!--            android:layout_marginTop="12dp"-->
        <!--            android:layout_marginEnd="8dp"-->
        <!--            android:layout_marginBottom="12dp"-->
        <!--            app:layout_constraintBottom_toTopOf="@+id/btnInstallLock"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toBottomOf="@+id/lockInstallations"-->
        <!--            app:layout_constraintVertical_bias="0.0" />-->

        <!--        <androidx.constraintlayout.widget.Guideline-->
        <!--            android:id="@+id/guideline5"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:orientation="vertical"-->
        <!--            app:layout_constraintGuide_percent="0.5" />-->


    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/layNoData"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="15dp"
        android:background="@drawable/white_top_corners"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/frameContainerHome">


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/iv_no_locks" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/no_locks_assigned"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rv_lock" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>