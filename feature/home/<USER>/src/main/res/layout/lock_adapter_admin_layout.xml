<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:layout_marginBottom="8dp"
    android:id="@+id/mainLay"
    android:theme="@style/Theme.MaterialComponents.Light"
    card_view:cardBackgroundColor="@color/card_bg_color"
    card_view:cardCornerRadius="10dp"
    card_view:cardElevation="0dp"
    card_view:cardMaxElevation="0dp"
    card_view:cardUseCompatPadding="true"
    card_view:strokeColor="@color/card_stroke_access"
    card_view:strokeWidth="1dp">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <TextView
            android:id="@+id/txtLockName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:includeFontPadding="false"
            android:textColor="@color/black"
            android:textSize="14dp"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txtModelName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="visible"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toBottomOf="@+id/txtLockName"
            card_view:layout_constraintVertical_bias="0.0" />

   <TextView
            android:id="@+id/txtStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="visible"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toBottomOf="@+id/txtModelName"
            card_view:layout_constraintVertical_bias="0.0" />

        <TextView
            android:id="@+id/txtDate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:layout_marginBottom="16dp"
            android:visibility="visible"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toBottomOf="@+id/txtStatus"
            card_view:layout_constraintVertical_bias="0.0" />


      

        <ImageView
            android:layout_width="38dp"
            android:layout_height="38dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/iv_cross_bg_white"
            android:padding="11dp"
            android:id="@+id/deleteAccess"
            android:src="@drawable/iv_trash_acess"
            card_view:layout_constraintBottom_toBottomOf="@+id/txtLockName"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintTop_toTopOf="@+id/txtLockName"
            card_view:layout_constraintVertical_bias="0.0" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</com.google.android.material.card.MaterialCardView>