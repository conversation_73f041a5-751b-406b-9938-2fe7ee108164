package feature.home.installer

import android.app.Dialog
import android.content.Intent
import android.graphics.Rect
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.JsonObject
import data.network.android.models.LocksModelInstaller
import data.network.android.models.ModelAdminInstaller
import data.utils.android.CommonValues
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.defaultDialog
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import feature.pm.addlock.AddLockMainActivity
import feature.properties.AddProperty
import keyless.feature.home.installer.R
import keyless.feature.home.installer.databinding.ActivityInstallerLockBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class InstallerLockActivity : AppCompatActivity(), AdapterLockList.ClickToAddComment {

    private var count: Int = 0
    private lateinit var model: ModelAdminInstaller.DataModelInstaller
    private lateinit var adapterLockList: AdapterLockList
    lateinit var viewModel: InstallerLockViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    lateinit var binding: ActivityInstallerLockBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInstallerLockBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initz()
        setAdapter()
        apiImplementation()
        clickListeners()
        observerInit()
    }

    private fun observerInit() {
        viewModel.error.observe(this) {
            toast(it)
        }

        viewModel.getResponseInstaller.observe(this) {
            if (it.data.isEmpty()) {
                binding.layNoData.isVisible = true
                binding.frameContainerHome.isVisible = false
            } else {
                for (i in it.data) {
                    if (i._id == model._id) {
                        model = i
                    } else {
                        binding.layNoData.isVisible = true
                        binding.frameContainerHome.isVisible = false
                    }
                }
                if (model.locks.size == 1) {
                    binding.lockInstallations.text =
                        getString(keyless.data.utils.android.R.string.pending_installation) + " " +
                        model.locks.size + " " + getString(keyless.data.utils.android.R.string.pendin_lock)
                } else {
                    binding.lockInstallations.text =
                        getString(keyless.data.utils.android.R.string.pending_installation) + " " + model.locks.size +
                        " " + getString(keyless.data.utils.android.R.string.pending_locks)
                }
                adapterLockList.updateList(model.locks)
//                layNoData.isVisible = false
//                frameContainerHome.isVisible = true
            }
        }

        viewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        viewModel.error.observe(this) {
            toast(it)
        }
    }

    private fun initz() {
        viewModel = ViewModelProvider(this)[InstallerLockViewModel::class.java]
        model = intent.getParcelableExtra("model")!!
        if (model.locks.size == 1) {
            binding.lockInstallations.text =
                getString(keyless.data.utils.android.R.string.pending_installation) + " " + model.locks.size +
                " " + getString(keyless.data.utils.android.R.string.pendin_lock)
        } else {
            binding.lockInstallations.text =
                getString(keyless.data.utils.android.R.string.pending_installation) + " " + model.locks.size + " " +
                getString(keyless.data.utils.android.R.string.pending_locks)
        }
        binding.txtDate.text = CommonValues.formateTimeDate1(model.installationDate.split(".")[0],this)
        if (model.slot == "morning") {
            binding.txtSlot.text = "Morning (9AM - 1PM)"
        } else {
            binding.txtSlot.text = "Evening (2PM - 6PM)"
        }
        binding.txtLockSizeColor.text = model.provider + " " + model.size + " " + model.colour + " color"
        binding.txtCompanyName.text = model.company[0].company_name
        binding.txtAddress.text = model.address
        binding.numberTotalLocks.text = " " + model.lockQty
        binding.numberPendingLocks.text = " " + model.locks.size.toString()

        if (!model.area_street.isNullOrBlank()) {
            binding.txtArea.text = model.area_street
            binding.txtArea.visibility = View.VISIBLE
            binding.txtAreaTitle.visibility = View.VISIBLE
        } else {
            binding.txtArea.visibility = View.GONE
            binding.txtAreaTitle.visibility = View.GONE
        }

        if (!model.building_villa_name.isNullOrBlank()) {
            binding.txtBuilding.text = model.building_villa_name
            binding.txtBuilding.visibility = View.VISIBLE
            binding.txtBuildingTitle.visibility = View.VISIBLE
        } else {
            binding.txtBuilding.visibility = View.GONE
            binding.txtBuildingTitle.visibility = View.GONE
        }

        if (!model.unit_number.isNullOrBlank()) {
            binding.txtUnit.text = model.unit_number
            binding.txtUnit.visibility = View.VISIBLE
            binding.txtUnitTitle.visibility = View.VISIBLE
        } else {
            binding.txtUnit.visibility = View.GONE
            binding.txtUnitTitle.visibility = View.GONE
        }

//        txtAddress.text = model.address
    }

    private fun apiImplementation() {
        viewModel.getProperty(sharePrefs.token, model.company[0]._id).observe(this) {
            if (it.success!!) {
//                count = it.lock_count!!
                count = if (it.lock_count != null) {
                    it.lock_count!!
                } else {
                    0
                }
            }
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        setResult(150)
        finish()
    }

    private fun clickListeners() {
        binding.backBtn.setOnClickListener {
            setResult(150)
            finish()
        }

        binding.imageView10.setOnClickListener {
            CommonValues.openPhone(this, model.contactPersonPhoneno)
        }

        binding.imgLocation.setOnClickListener {
            if (model.lat.isNotEmpty()) {
                val strUri =
                    "http://maps.google.com/maps?q=loc:" + model.lat + "," + model.long + " (" + "Location" + ")"
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(strUri))
                startActivity(intent)
            }
        }

        binding.btnInstallLock.setOnClickListener {
            if (count == 0) {
                defaultDialog(
                    this,
                    getString(keyless.data.utils.android.R.string.must_add_building),
                    object : OnActionOK {
                        override fun onClickData() {
                            startActivityForResult(
                                Intent(
                                    this@InstallerLockActivity,
                                    AddProperty::class.java
                                ).putExtra("installer", model),
                                70
                            )
                        }
                    }
                )
            } else {
                startActivityForResult(
                    Intent(
                        this,
                        AddLockMainActivity::class.java
                    ).putExtra("installer", "installer").putExtra("list", model),
                    60
                )
            }
        }

        binding.btnAddProperty.setOnClickListener {
            startActivityForResult(
                Intent(this, feature.properties.AddProperty::class.java).putExtra(
                    "installer",
                    model
                ),
                70
            )
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == 20) {
            apiImplementation()
        } else if (requestCode == 60 && resultCode == RESULT_OK) {
            viewModel.getInstallerList(sharePrefs.token, 1, "", false, model._id)
            setResult(150)
//            finish()
        }
    }

    private fun setAdapter() {
        binding.txtName.text = model.contactPersonName
        binding.txtPhoneNumber.text = model.contactPersonPhoneno
        binding.titlePage.text = model.company[0].company_name
        binding.rvLockList.layoutManager = LinearLayoutManager(this)
        adapterLockList = AdapterLockList(this)
        binding.rvLockList.adapter = adapterLockList
        adapterLockList.updateList(model.locks)
    }

    override fun addComment(modelLock: LocksModelInstaller, position: Int) {
        val dialog = Dialog(this)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.setBackgroundDrawableResource(keyless.feature.common.R.drawable.white_all_corners_10)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.dialog_error_installation)
        val txtMarkAsRead = dialog.findViewById<TextView>(R.id.txtMarkAsRead)
        val etIssue = dialog.findViewById<EditText>(R.id.etIssue)
        val cancelBtn = dialog.findViewById<TextView>(R.id.cancelBtn)
        dialog.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialog.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
            val maxWidth = displayRectangle.width() * 0.8f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight)
        }

        txtMarkAsRead.setOnClickListener {
            if (etIssue.text.toString().trim().isEmpty()) {
                toast(getString(keyless.data.utils.android.R.string.comment_should_not_be_empty))
            } else {
                val jsonObject = JsonObject()
                jsonObject.addProperty("status", "2")
                jsonObject.addProperty("company_id", model.company[0]._id)
                jsonObject.addProperty("lock_id", modelLock._id)
                jsonObject.addProperty("installation_id", model._id)
                jsonObject.addProperty("comment", etIssue.text.toString())
                ProgressDialogUtils.getInstance().showProgress(this@InstallerLockActivity, true)
                viewModel.updateStatus(sharePrefs.token, jsonObject).observe(this) {
                    ProgressDialogUtils.getInstance().hideProgress()
                    dialog.dismiss()
                    if (it.success) {
                        lifecycleScope.launch {
                            delay(200)
                            defaultDialog(
                                this@InstallerLockActivity,
                                it.message,
                                object : OnActionOK {
                                    override fun onClickData() {
                                        adapterLockList.updateStatusComment(
                                            etIssue.text.toString(),
                                            position
                                        )
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }

        cancelBtn.setOnClickListener {
            dialog.dismiss()
        }
        dialog.show()
    }
}