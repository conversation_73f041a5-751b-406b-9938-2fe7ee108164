package feature.home.properties

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import data.network.android.ApiUtils
import data.network.android.LocksListResponse
import data.utils.android.hideKeyboard
import keyless.feature.home.properties.R
import keyless.feature.home.properties.databinding.PropertiesAdapterLayoutBinding

class PropertiesAdapter(
    var arrayProperties: ArrayList<LocksListResponse.PropertiesMainModel>,
    var homeMainList: ArrayList<LocksListResponse.LocksModel>,
    var contextMain: Fragment
) :
    RecyclerView.Adapter<PropertiesAdapter.ViewHolder>(), Filterable {

    private var charString: String = ""
    var selectedIndex: Int? = -1
    lateinit var context: Context
    var listFiltered = arrayProperties

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = PropertiesAdapterLayoutBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(listFiltered[position], position)
    }

    private fun setUpDetailsRecyclerView(
        binding: PropertiesAdapterLayoutBinding,
        position: Int,
        context: Context?,
        filteredLockList: ArrayList<LocksListResponse.LocksModel>,
        count: Int
    ) {
        if (charString.isEmpty()) {
            binding.view.visibility = View.GONE
            binding.rvPropertiesDetail.visibility = View.GONE
        }
        binding.icon.isVisible = count > 0
        binding.rvPropertiesDetail.layoutManager = GridLayoutManager(context, 2)
        val adapter = LockAdapter(contextMain)
        binding.rvPropertiesDetail.adapter = context?.let { adapter }
        adapter.update(filteredLockList)

        if (position == selectedIndex) {
            binding.rvPropertiesDetail.visibility = View.VISIBLE
            binding.view.visibility = View.GONE
            binding.icon.setBackgroundDrawable(
                context?.let {
                    ContextCompat.getDrawable(
                        it,
                        R.drawable.ic_arrow_upward
                    )
                }
            )
        } else {
            binding.rvPropertiesDetail.visibility = View.GONE
            binding.view.visibility = View.GONE
            binding.icon.setBackgroundDrawable(
                context?.let {
                    ContextCompat.getDrawable(
                        it,
                        keyless.data.utils.android.R.drawable.ic_arrow_forward
                    )
                }
            )
        }
    }

    override fun getItemCount() = listFiltered.size

    inner class ViewHolder(val binding: PropertiesAdapterLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: LocksListResponse.PropertiesMainModel, position: Int) {
            if (listFiltered[position].count <= 1) {
                binding.txtDeviceCount.text =
                    listFiltered[position].count.toString() + " " + context.getString(
                    keyless.data.utils.android.R.string.text_lock
                )
            } else {
                binding.txtDeviceCount.text =
                    listFiltered[position].count.toString() + " " + context.getString(
                    keyless.data.utils.android.R.string.text_locks
                )
            }

            binding.txtLocation.text = listFiltered[position].building_name
            if (listFiltered[position].icon.size > 0) {
                if (listFiltered[position].icon[0].icon.isNotEmpty()) {
                    Glide.with(context)
                        .load(ApiUtils.IMAGE_BASE_URL + listFiltered[position].icon[0].icon)
                        .placeholder(keyless.feature.common.R.drawable.iv_other_icon)
                        .into(binding.ivPropertyIcon)
                } else {
                    binding.ivPropertyIcon.setImageResource(keyless.feature.common.R.drawable.iv_other_icon)
                }
            } else {
                binding.ivPropertyIcon.setImageResource(keyless.feature.common.R.drawable.iv_other_icon)
            }

            val filteredLockList = ArrayList<LocksListResponse.LocksModel>()
            for (i in homeMainList) {
                if (i.property_details.id == listFiltered[position]._id) {
                    filteredLockList.add(i)
                }
            }

            setUpDetailsRecyclerView(
                binding,
                position,
                context,
                filteredLockList,
                listFiltered[position].count
            )
            binding.mainLay.setOnClickListener {
                context.hideKeyboard(binding.mainLay)
                if (listFiltered[position].count > 0) {
                    selectedIndex = if (selectedIndex == position) {
                        null
                    } else {
                        position
                    }

                    notifyDataSetChanged()
                }
            }
        }
    }

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                charString = constraint?.toString() ?: ""
                if (charString == "") {
                    selectedIndex = -1
                }
                listFiltered = if (charString.isEmpty()) {
                    arrayProperties
                } else {
                    val mFilteredList = ArrayList<LocksListResponse.PropertiesMainModel>()
                    arrayProperties.filter {
                        (it.building_name.contains(constraint!!, true)) or
                            (it.building_name.startsWith(constraint, true))
                    }
                        .forEach { mFilteredList.add(it) }
                    mFilteredList
                }
                return FilterResults().apply { values = listFiltered }
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                listFiltered = if (results?.values == null) {
                    ArrayList()
                } else {
                    results.values as java.util.ArrayList<LocksListResponse.PropertiesMainModel>
                }
                notifyDataSetChanged()
            }
        }
    }
}