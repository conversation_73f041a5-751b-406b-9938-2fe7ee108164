<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/backBtnDevice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:padding="20dp"
        style="@style/ImageMirror"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/propertyTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/select_lock"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtnDevice"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtnDevice" />

    <FrameLayout
        android:layout_marginTop="10dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtnDevice"
        app:layout_constraintVertical_bias="0.0">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:elevation="0dp"
            app:cardCornerRadius="32dp"
            app:cardElevation="0dp">


            <TextView
                android:id="@+id/noData"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:drawableTop="@drawable/iv_no_locks"
                android:fontFamily="@font/poppins_semibold_600"
                android:gravity="center"
                android:drawablePadding="15dp"
                android:includeFontPadding="false"
                android:visibility="invisible"
                android:text="@string/no_locks_found"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/deviceRV"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:paddingStart="5dp"
                android:paddingTop="15dp"
                android:paddingEnd="5dp"
                android:paddingBottom="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />



            <LinearLayout
                android:layout_width="match_parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:gravity="center"
                android:orientation="vertical"
                android:background="@color/transparent"
                android:focusable="true"
                android:visibility="gone"
                android:id="@+id/deviceProgressBar"
                android:clickable="true"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_height="match_parent">

                <ProgressBar
                    android:visibility="visible"
                    android:layout_gravity="center"
                    android:theme="@style/progressBarBlue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />


                <TextView
                    android:layout_width="wrap_content"
                    android:id="@+id/progressTxt"
                    android:text="@string/connecting"
                    style="@style/mirrorText"
                    android:fontFamily="@font/poppins_medium_500"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"/>

            </LinearLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

    </FrameLayout>



</androidx.constraintlayout.widget.ConstraintLayout>