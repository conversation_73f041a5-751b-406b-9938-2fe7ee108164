name: "Inject keyless secrets"
description: "Inject keyless secrets"
inputs:
  shell:
    required: true
    description: "Shell to use depending on runner os"
  sedVariant:
    required: true
    description: "Sed variant to use depending on runner os"

  environment:
    required: false
    description: "Environment"
    default: "Development"

  keylessBaseApiUrl:
    description: "Keyless base api url"
    required: true
  keylessBaseImageUrl:
    description: "Keyless base image url"
    required: true
  keylessForgotPasswordUrl:
    description: "Keyless forgot password url"
    required: true

runs:
  using: "composite"
  steps:
    - name: Inject keyless secrets
      run: |
        touch local.properties
        
        echo environment=${{ inputs.environment }} >> local.properties
        
        echo base.url=${{ inputs.keylessBaseApiUrl }} >> local.properties
        echo base.image.url=${{ inputs.keylessBaseImageUrl }} >> local.properties
        echo forgot.password.url=${{ inputs.keylessForgotPasswordUrl }} >> local.properties

      shell: ${{ inputs.shell }}