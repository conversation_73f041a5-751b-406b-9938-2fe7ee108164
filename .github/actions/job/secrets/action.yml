name: "Inject project secrets"
description: "Inject secrets"
inputs:
  shell:
    required: true
    description: "Shell to use depending on runner os"
  sedVariant:
    required: true
    description: "Sed variant to use depending on runner os"

  firebaseKeylessApiKey:
    description: "Firebase api key"
    required: true
  firebaseKeylessProjectId:
    description: "Firebase keyless project id"
    required: true
  firebaseKeylessProjectNumber:
    description: "Firebase keyless project number"
    required: true
  firebaseKeylessStorageBucket:
    description: "Firebase keyless storage bucket"
    required: true

  environment:
    required: false
    description: "Environment"
    default: "Development"

  keylessBaseApiUrl:
    description: "keyless base api url"
    required: true
  keylessBaseImageUrl:
    description: "keyless base image url"
    required: true
  keylessForgotPasswordUrl:
    description: "Keyless forgot password url"
    required: true

  releaseSigningKeyAlias:
    description: "Release signing key alias"
    required: true
  releaseSigningKeyPassword:
    description: "Release signing key password"
    required: true
  releaseSigningKeyStoreFile:
    description: "Release signing key store file"
    required: true
  releaseSigningKeyStoreFileContent:
    description: "Release signing key store file content"
    required: true
  releaseSigningKeyStorePassword:
    description: "Release signing key store password"
    required: true

runs:
  using: "composite"
  steps:
    - name: Inject keyless secrets
      uses: ./.github/actions/job/secrets/keyless
      with:
        environment: ${{ inputs.environment }}

        keylessBaseApiUrl: ${{ inputs.keylessBaseApiUrl }}
        keylessBaseImageUrl: ${{ inputs.keylessBaseImageUrl }}
        keylessForgotPasswordUrl: ${{ inputs.keylessForgotPasswordUrl }}

        shell: ${{ env.shell }}
        sedVariant: ${{ env.sed }}

    - name: Inject release secrets
      uses: ./.github/actions/job/secrets/release
      with:
        releaseSigningKeyAlias: ${{ inputs.releaseSigningKeyAlias }}
        releaseSigningKeyPassword: ${{ inputs.releaseSigningKeyPassword }}
        releaseSigningKeyStoreFile: ${{ inputs.releaseSigningKeyStoreFile }}
        releaseSigningKeyStoreFileContent: ${{ inputs.releaseSigningKeyStoreFileContent }}
        releaseSigningKeyStorePassword: ${{ inputs.releaseSigningKeyStorePassword }}

        shell: ${{ env.shell }}
        sedVariant: ${{ env.sed }}