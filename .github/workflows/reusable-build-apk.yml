name: Build apk

on:
  workflow_call:
    inputs:
      apkPath:
        required: true
        description: "Path to the apk"
        type: string
      assembleVariant:
        required: true
        description: "Build variant to use"
        type: string
      environment:
        required: true
        description: "Environment to build for"
        type: string
      versionName:
        required: true
        description: "Version name to use"
        type: string

      artifactUploadApkName:
        required: true
        description: "name of artifact upload"
        type: string

    outputs:
      apkPath:
        description: "Path to the apk"
        value: ${{ jobs.build.outputs.apkPath }}
      apkName:
        description: "Name of the apk"
        value: ${{ jobs.build.outputs.apkName }}

jobs:
  build:
    name: Build
    runs-on: ubuntu-22.04
    env:
      sed: "sed"
      shell: "bash"
    environment:
      name: ${{ inputs.environment }}
    permissions:
      actions: read
      contents: read
      deployments: write
    outputs:
      apkPath: ${{ steps.uploadApk.outputs.filePath }}
      apkName: ${{ steps.uploadApk.outputs.fileName }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.SUBMODULE_ACCESS_TOKEN }}
          submodules: true

      - name: Inject project secrets
        uses: ./.github/actions/job/secrets
        with:
          environment: ${{ inputs.environment }}

          keylessBaseApiUrl: ${{ secrets.BASE_API_URL }}
          keylessBaseImageUrl: ${{ secrets.BASE_IMAGE_URL }}
          keylessForgotPasswordUrl: ${{ secrets.FORGOT_PASSWORD_URL }}

          releaseSigningKeyAlias: ${{ secrets.KEYLESS_ANDROID_SIGNING_ALIAS }}
          releaseSigningKeyPassword: ${{ secrets.KEYLESS_ANDROID_SIGNING_PASSWORD }}
          releaseSigningKeyStoreFile: "app/release.jks"
          releaseSigningKeyStoreFileContent: ${{ secrets.KEYLESS_ANDROID_SIGNING_KEYSTORE }}
          releaseSigningKeyStorePassword: ${{ secrets.KEYLESS_ANDROID_SIGNING_KEYSTORE_PASSWORD }}

          shell: ${{ env.shell }}
          sedVariant: ${{ env.sed }}

      - name: Inject android google service
        uses: ./.github/actions/job/file/create
        with:
          filePath: app/google-services.json
          fileContent: ${{ secrets.ANDROID_FIREBASE_GOOGLE_SERVICE }}

      - name: Init JDK
        uses: ./.github/actions/job/setup-jdk

      - name: Init cache
        uses: ./.github/actions/job/setup-cache

      - name: Init gradle
        uses: ./.github/actions/gradle/setup-gradle

      - name: Init version name
        id: init-version-name
        uses: ./.github/actions/job/release-version
        with:
          refName: ${{ inputs.versionName }}
          shell: ${{ env.shell }}

      - name: Bump version code
        uses: ./.github/actions/gradle/bump-android-version
        with:
          versionCode: ${{ github.run_number }}
          versionName: ${{ steps.init-version-name.outputs.versionName }}
          gradlePath: "app/build.gradle.kts"

      - name: Assemble apk
        uses: ./.github/actions/gradle/assemble
        with:
          shell: ${{ env.shell }}
          assembleVariant: ${{ inputs.assembleVariant }}

      - name: Upload built apk
        id: uploadApk
        uses: ./.github/actions/job/artifact-upload
        with:
          uploadName: ${{ inputs.artifactUploadApkName }}
          uploadPath: ${{ inputs.apkPath }}
          shell: ${{ env.shell }}