package core.http.client.test

import core.common.platform.Platform
import core.http.client.HttpClient
import core.http.client.HttpError
import core.http.client.HttpRequest
import core.http.client.HttpResponse
import core.http.client.test.models.TestRequestResponse
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.log
import java.lang.Exception

class MockHttpClient(val logger: Logger) : HttpClient {

    private val responses = arrayListOf<TestRequestResponse>()

    fun setup(vararg responses: TestRequestResponse) {
        this.responses.addAll(responses)
    }

    override suspend fun request(request: HttpRequest): HttpResponse = logger.log {
        val response = responses.find {
            it.request.url == request.url && it.request.method == request.method
        }

        if (response == null) {
            throw HttpError(
                message = "Mock client not setup",
                callLocation = Platform.executeLocation(),
                details = mapOf(),
                responseBody = "",
                responseCode = 404,
                cause = Exception("Mock client not setup"),
                responseCodeDescription = "Not Found"
            )
        }

        if (response.response.responseCode >= 299) {
            throw HttpError(
                message = "Mock client not setup",
                callLocation = Platform.executeLocation(),
                details = mapOf(),
                responseBody = "",
                responseCode = 404,
                cause = Exception("Mock client not setup"),
                responseCodeDescription = "Not Found"
            )
        }

        return@log HttpResponse(
            request = request,
            responseHeaders = mapOf(),
            responseCode = response.response.responseCode,
            body = response.response.body
        )
    }

    override suspend fun formRequest(request: HttpRequest): HttpResponse {
        return request(request)
    }

    override suspend fun unsafeRequest(request: HttpRequest): HttpResponse {
        return request(request)
    }
}