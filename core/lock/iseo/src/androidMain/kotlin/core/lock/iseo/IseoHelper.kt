package core.lock.iseo

import android.content.Context
import com.iseo.v364droidsdk.AndroidContext
import core.caching.KeyValueCache

object IseoHelper {

    internal val ISEO_URL_KEY = "iseo_url_key"
    internal val PLANT_NAME_KEY = "plant_name_key"

    fun setContext(context: Context) {
        AndroidContext.setContext(context)
    }

    fun saveCredentials(settings: KeyValueCache, iseoUrl: String, plantName: String) {
        settings.setString(ISEO_URL_KEY, iseoUrl)
        settings.setString(PLANT_NAME_KEY, plantName)
    }
}