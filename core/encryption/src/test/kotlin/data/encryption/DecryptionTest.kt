package data.encryption

import kotlin.test.Test
import kotlin.test.assertEquals

class DecryptionTest {

    @Test
    fun testLockDetailsDecryption() {
        assertEquals(
            expected = "mb55",
            actual = Decryption.decrypt(
                "U2FsdGVkX1/hPTU5GVHG0HDkjD8OPp79fdzB7eTTvek=",
                "293b8edeef5944fcb729def8b384c2f8"
            )
        )

        assertEquals(
            expected = "mb55",
            actual = Decryption.decrypt(
                "U2FsdGVkX19Iejt6mP2zMDsosDG5cthwveMVHM4h7hU=",
                "5756eb5aa17a44eb935c1c9f3c78bdf0"
            )
        )
    }
}