@file:Suppress("PackageDirectoryMismatch", "unused")

import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

class KoinAndroidConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) = with(target) {
        pluginManager.apply("keyless.koin")

        dependencies {
            add("implementation", catalog.findLibrary("koin.android").get())
            add("implementation", catalog.findLibrary("koin.android.compat").get())
            add("implementation", catalog.findLibrary("koin.androidx.navigation").get())
            add("implementation", catalog.findLibrary("koin.androidx.workmanager").get())

            add("testImplementation", catalog.findLibrary("koin.android.test").get())
        }
    }
}