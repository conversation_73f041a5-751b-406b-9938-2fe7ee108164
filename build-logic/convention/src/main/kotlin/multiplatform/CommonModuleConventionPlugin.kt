import org.gradle.api.JavaVersion
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.plugins.JavaPluginExtension
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.getByType
import org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformExtension
import org.jetbrains.kotlin.gradle.plugin.KotlinDependencyHandler

@Suppress("unused")
class CommonModuleConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) = with(target) {
        val extension by lazy {
            extensions.getByType<KotlinMultiplatformExtension>()
        }
        val commonMain by lazy { extension.sourceSets.getByName("commonMain") }
        val commonTest by lazy { extension.sourceSets.getByName("commonTest") }

        pluginManager.apply(catalog.findPlugin("kotlin-multiplatform").get().get().pluginId)
        pluginManager.apply(catalog.findPlugin("kotlin-serialization").get().get().pluginId)

        commonMain.dependencies {
            implementation(catalog.findLibrary("kotlinx.coroutines.core").get())
            implementation(catalog.findLibrary("kotlinx.serialization.json").get())
            implementation(catalog.findLibrary("kotlinx.datetime").get())
        }

        commonTest.dependencies {
            implementation(catalog.findLibrary("kotlin.test").get())
            implementation(catalog.findLibrary("kotlinx.coroutines.test").get())
        }

        extensions.configure<JavaPluginExtension> {
            sourceCompatibility = JavaVersion.toVersion(catalog.findVersion("javaCompatibility").get().displayName)
            targetCompatibility = JavaVersion.toVersion(catalog.findVersion("javaCompatibility").get().displayName)
        }
    }
}

@Suppress("unused")
fun Project.commonDependencies(scope: KotlinDependencyHandler.() -> Unit) {
    val extension by lazy {
        extensions.getByType<KotlinMultiplatformExtension>()
    }
    val commonMain by lazy { extension.sourceSets.getByName("commonMain") }

    commonMain.dependencies { scope() }
}

@Suppress("unused")
fun Project.commonTestDependencies(scope: KotlinDependencyHandler.() -> Unit) {
    val extension by lazy {
        extensions.getByType<KotlinMultiplatformExtension>()
    }
    val commonTest by lazy { extension.sourceSets.getByName("commonTest") }

    commonTest.dependencies { scope() }
}