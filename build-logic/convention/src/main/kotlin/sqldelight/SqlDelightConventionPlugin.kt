@file:Suppress("PackageDirectoryMismatch")

import org.gradle.api.Plugin
import org.gradle.api.Project

class SqlDelightConventionPlugin : Plugin<Project> {

    override fun apply(target: Project): Unit = with(target) {
        pluginManager.apply("app.cash.sqldelight")

        dependencies.add("api", catalog.findLibrary("sqldelight.runtime").get())
        dependencies.add("api", catalog.findLibrary("sqldelight.coroutines.extensions").get())
    }
}