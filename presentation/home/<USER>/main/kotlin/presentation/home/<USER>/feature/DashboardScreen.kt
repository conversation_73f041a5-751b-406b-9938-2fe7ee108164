package presentation.home.dashboard.feature

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.tooling.preview.Preview
import presentation.common.feature.components.AppBasePage
import presentation.common.feature.components.AppScrollColumn
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.theme.AppTheme

@Composable
internal fun DashboardScreen(state: StateHolder) {
    AppBasePage(
        topBar = { HomeTopBar(state) }
    ) {
        AppScrollColumn(
            arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
        ) {
            item { GuestCarousel(state = state) }

            if (state.ui.changePassword.get() is ChangePasswordState.Show) {
                item {
                    ChangePasswordRow(
                        state = state.ui.changePassword.get() as ChangePasswordState.Show,
                        onChangePassword = {}
                    )
                }
            }

            item { YourKeysRow(state = state, onClaimKey = {}) }
        }
    }
}

@Preview
@Composable
private fun Preview() {
    val state = remember {
        val state = StateHolder()
        state.ui.changePassword.update(ChangePasswordState.Show("2021/12/12"))
        state
    }
    AppTheme { DashboardScreen(state) }
}