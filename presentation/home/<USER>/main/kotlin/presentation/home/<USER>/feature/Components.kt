package presentation.home.dashboard.feature

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import keyless.presentation.common.R
import presentation.common.feature.components.AppCarousel
import presentation.common.feature.components.AppCarouselIndicator
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppFlowRow
import presentation.common.feature.components.AppIcon
import presentation.common.feature.components.AppImage
import presentation.common.feature.components.AppLabelText
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppRow
import presentation.common.feature.components.AppSurface
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.components.hiddenClickable

@Composable
internal fun BoxScope.HomeTopBar(state: StateHolder) {
    GuestTopBar(state)
}

@Composable
internal fun BoxScope.GuestTopBar(state: StateHolder) {
    AppRow(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = DesignSystem.Padding.large, vertical = DesignSystem.Padding.medium),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
        alignment = Alignment.CenterVertically
    ) {
        AppPageTitleText(
            modifier = Modifier.weight(1f),
            text = state.ui.welcomeText(),
            design = DesignSystem.Text.PageTitle.copy(overflow = TextOverflow.Ellipsis)
        )

        AppIcon(res = R.drawable.iv_notifications)

        AppIcon(res = R.drawable.iv_settings)
    }
}

@Composable
internal fun GuestCarousel(modifier: Modifier = Modifier, state: StateHolder) {
    AppCarousel(
        modifier = modifier,
        count = 3,
        contentPadding = PaddingValues(0.dp),
        gridSpacing = DesignSystem.Padding.screen
    ) {
        when (it) {
            0 -> GuestCarouselItem(
                index = it,
                titleRes = R.string.welcome_to_keyless,
                textRes = R.string.one_application
            )

            1 -> GuestCarouselItem(index = it, titleRes = R.string.share_access, textRes = R.string.keyless_enables)
            2 -> GuestCarouselItem(index = it, titleRes = R.string.quick_services, textRes = R.string.choose_from_a)
        }
    }
}

@Composable
internal fun YourKeysRow(modifier: Modifier = Modifier, state: StateHolder, onClaimKey: () -> Unit) {
    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.SpaceBetween,
        alignment = Alignment.CenterVertically
    ) {
        AppLabelText(
            text = stringResource(R.string.your_keys),
            design = DesignSystem.Text.Label.copy(weight = FontWeight.Bold)
        )

        AppLabelText(
            modifier = Modifier
                .clip(CircleShape)
                .border(1.dp, MaterialTheme.colorScheme.primary, CircleShape)
                .padding(horizontal = DesignSystem.Padding.medium, vertical = DesignSystem.Padding.small)
                .clickable(onClick = onClaimKey),
            text = stringResource(R.string.claim_your_key),
            design = DesignSystem.Text.Label.copy(color = MaterialTheme.colorScheme.primary, weight = FontWeight.Bold)
        )
    }
}

@Composable
internal fun ChangePasswordRow(
    modifier: Modifier = Modifier,
    state: ChangePasswordState.Show,
    onChangePassword: () -> Unit
) {
    AppSurface(
        modifier = modifier.hiddenClickable(onClick = onChangePassword),
        paddings = PaddingValues(horizontal = DesignSystem.Padding.large, vertical = DesignSystem.Padding.medium),
        color = MaterialTheme.colorScheme.errorContainer
    ) {
        AppLabelText(
            text = stringResource(R.string.tap_to_update_your_password_before, state.dueDate),
            design = DesignSystem.Text.Label.copy(color = MaterialTheme.colorScheme.error, alignment = TextAlign.Center)
        )
    }
}

@Composable
internal fun LocksSection(modifier: Modifier = Modifier, state: StateHolder) {
    AppFlowRow(modifier = modifier) {
        for (lock in state.ui.locks.value) {

        }
    }
}

@Composable
private fun LockItem()

@Composable
private fun GuestCarouselItem(
    modifier: Modifier = Modifier,
    index: Int,
    titleRes: Int,
    textRes: Int
) {
    AppSurface(
        modifier = modifier, paddings = PaddingValues(DesignSystem.Padding.screen),
        color = MaterialTheme.colorScheme.tertiary
    ) {
        AppColumn(
            modifier = Modifier.fillMaxWidth(),
            arrangement = Arrangement.spacedBy(DesignSystem.Padding.large),
            alignment = Alignment.CenterHorizontally
        ) {
            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
                alignment = Alignment.CenterVertically
            ) {
                AppImage(res = R.drawable.keyless_logo_2)

                AppColumn(
                    modifier = Modifier.weight(1f),
                    arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
                    alignment = Alignment.CenterHorizontally
                ) {
                    AppPageTitleText(text = stringResource(titleRes))
                    AppLabelText(
                        text = stringResource(textRes),
                        design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center, minLines = 3)
                    )
                }
            }

            AppCarouselIndicator(count = 3, currentPage = index, color = MaterialTheme.colorScheme.primary)
        }
    }
}