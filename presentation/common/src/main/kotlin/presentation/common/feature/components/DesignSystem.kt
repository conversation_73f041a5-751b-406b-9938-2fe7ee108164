package presentation.common.feature.components

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import presentation.common.feature.components.DesignSystem.Corner
import presentation.common.feature.components.DesignSystem.Padding

private typealias ComposeColor = Color

object DesignSystem {

    object Padding {
        val small = 4.dp
        val medium = 8.dp
        val large = 24.dp
        val screen = 19.dp
    }

    object Corner {
        val small = 4.dp
        val medium = 8.dp
        val large = 16.dp

        val subScreen = 32.dp
        var buttonShape: Shape = CircleShape
    }

    data class Color(
        val light: androidx.compose.ui.graphics.Color,
        val dark: androidx.compose.ui.graphics.Color = light
    ) {
        val get: androidx.compose.ui.graphics.Color @Composable get() = if (isSystemInDarkTheme()) dark else light

        companion object {
            val primary = Color(0xFF001C55)
            val secondary = Color(0xFFCDBC7A)
            val tertiary = Color(0xFF001C55)
        }
    }

    data class Screen(
        val horizontalPadding: Dp = Padding.screen,
        val verticalPadding: Dp = Padding.screen,
        val cornerRadius: Dp = Corner.subScreen
    ) {
        companion object {
            val Default = Screen()
        }
    }

    object TextField {
        val NormalTextField @Composable get() = TextFieldDesign.NormalTextField.Default
    }

    object Text {
        val Body @Composable get() = TextDesign.Body.Default
        val Label @Composable get() = TextDesign.Label.Default
        val PageTitle @Composable get() = TextDesign.PageTitle.Default
    }

    object Button {
        val Normal @Composable get() = ButtonDesign.Normal.Default
    }

    object TextButton {
        val Normal @Composable get() = TextButtonDesign.Normal.Default
    }
}

sealed interface TextDesign {
    val color: ComposeColor
    val weight: FontWeight
    val size: TextUnit
    val alignment: TextAlign
    val lineHeight: TextUnit
    val textDecoration: TextDecoration
    val minLines: Int
    val overflow: TextOverflow

    data class Body(
        override val color: ComposeColor,
        override val size: TextUnit,
        override val alignment: TextAlign = TextAlign.Start,
        override val weight: FontWeight = FontWeight.Medium,
        override val textDecoration: TextDecoration = TextDecoration.None,
        override val minLines: Int = 1,
        override val overflow: TextOverflow = TextOverflow.Clip
    ) : TextDesign {
        override val lineHeight: TextUnit = TextUnit.Unspecified

        companion object {
            val Default
                @Composable get() = Body(
                    LocalContentColor.current,
                    MaterialTheme.typography.bodyMedium.fontSize
                )
        }
    }

    data class Label(
        override val color: ComposeColor,
        override val size: TextUnit,
        override val alignment: TextAlign = TextAlign.Start,
        override val textDecoration: TextDecoration = TextDecoration.None,
        override val overflow: TextOverflow = TextOverflow.Clip,
        override val minLines: Int = 1,
        override val weight: FontWeight = FontWeight.Normal
    ) : TextDesign {
        override val lineHeight: TextUnit = (size.value + 2).sp

        companion object {
            val Default
                @Composable get() = Label(
                    LocalContentColor.current,
                    MaterialTheme.typography.labelMedium.fontSize
                )
        }
    }

    data class PageTitle(
        override val color: ComposeColor,
        override val alignment: TextAlign = TextAlign.Start,
        override val textDecoration: TextDecoration = TextDecoration.None,
        override val overflow: TextOverflow = TextOverflow.Clip
    ) : TextDesign {
        override val minLines: Int = 1
        override val lineHeight: TextUnit = TextUnit.Unspecified
        override val weight: FontWeight = FontWeight.Bold
        override val size: TextUnit = 20.sp

        companion object {
            val Default @Composable get() = PageTitle(LocalContentColor.current)
        }
    }
}

sealed interface TextFieldDesign {
    val backgroundColor: ComposeColor
    val shape: Shape
    val minHeight: Dp
    val padding: Dp
    val keyboardOptions: KeyboardOptions
    val keyboardActions: KeyboardActions

    data class NormalTextField constructor(
        override val backgroundColor: ComposeColor,
        override val keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
        override val keyboardActions: KeyboardActions = KeyboardActions.Default
    ) : TextFieldDesign {
        override val shape: RoundedCornerShape = RoundedCornerShape(Corner.medium)
        override val minHeight: Dp = 56.dp
        override val padding: Dp = Padding.medium

        companion object {
            val Default @Composable get() = NormalTextField(MaterialTheme.colorScheme.surfaceVariant)
        }
    }
}

sealed interface ButtonDesign {
    val backgroundColor: ComposeColor
    val contentColor: ComposeColor
    val shape: Shape
    val minHeight: Dp
    val padding: Dp

    data class Normal(
        override val backgroundColor: ComposeColor,
        override val contentColor: ComposeColor
    ) : ButtonDesign {
        override val shape: Shape = DesignSystem.Corner.buttonShape
        override val minHeight: Dp = 52.dp
        override val padding: Dp = Padding.medium

        companion object {
            val Default
                @Composable get() = Normal(
                    backgroundColor = MaterialTheme.colorScheme.secondary,
                    contentColor = MaterialTheme.colorScheme.onSecondary
                )
        }
    }
}

sealed interface TextButtonDesign {
    val contentColor: ComposeColor
    val shape: Shape
    val minHeight: Dp
    val padding: Dp

    data class Normal(
        override val contentColor: ComposeColor
    ) : TextButtonDesign {
        override val shape: RoundedCornerShape = CircleShape
        override val minHeight: Dp = 52.dp
        override val padding: Dp = Padding.medium

        companion object {
            val Default
                @Composable get() = Normal(contentColor = MaterialTheme.colorScheme.primary)
        }
    }
}